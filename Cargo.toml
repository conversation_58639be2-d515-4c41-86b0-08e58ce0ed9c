[package]
name = "stress_tool"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core dependencies
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
thiserror = "1.0"

# Async and concurrency
futures = "0.3"
tokio-stream = "0.1"

# Metrics and statistics
hdrhistogram = "7.0"

# Multimodal processing
image = { version = "0.24", features = ["jpeg", "png", "webp"] }
base64 = "0.21"

# Optional video processing (feature-gated)
opencv = { version = "0.88", optional = true }

# Tokenizer support
tiktoken-rs = { version = "0.5", optional = true }
tokenizers = { version = "0.15", optional = true }

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration file support
serde_yaml = "0.9"
toml = "0.8"

# File system and path handling
walkdir = "2.0"

# Date and time handling
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
tempfile = "3.8"

[features]
default = ["tiktoken"]
tiktoken = ["tiktoken-rs"]
hf-tokenizers = ["tokenizers"]
video = ["opencv"]
