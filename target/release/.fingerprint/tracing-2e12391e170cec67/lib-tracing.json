{"rustc": 12610991425282158916, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 10369491684090452477, "path": 946218741192612267, "deps": [[325572602735163265, "tracing_attributes", false, 658912855066134112], [1906322745568073236, "pin_project_lite", false, 13086803154891224887], [3424551429995674438, "tracing_core", false, 4370362745076046324]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tracing-2e12391e170cec67/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}