{"rustc": 12610991425282158916, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 17027912443588304793, "deps": [[9620753569207166497, "zerovec_derive", false, 14419469473831894942], [10706449961930108323, "yoke", false, 11363603483978789668], [17046516144589451410, "zerofrom", false, 11238549038546878738]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zerovec-4d93715f0ed3cf04/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}