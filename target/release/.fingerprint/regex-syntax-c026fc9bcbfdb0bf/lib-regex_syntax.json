{"rustc": 12610991425282158916, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 2040997289075261528, "path": 10870962208322495524, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-syntax-c026fc9bcbfdb0bf/dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}