{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 9298903534527576498, "path": 7091902801157200386, "deps": [[4684437522915235464, "libc", false, 17752158609176823018], [7896293946984509699, "bitflags", false, 18438304855155704239], [8253628577145923712, "libc_errno", false, 17667878108416583609], [10004434995811528692, "build_script_build", false, 15512977165402921191]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-aa2aaf9c3cec78aa/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}