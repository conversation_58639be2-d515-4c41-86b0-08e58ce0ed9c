{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 14724931235169721617, "path": 17965434717085506856, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-a1dea0af3cd92646/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}