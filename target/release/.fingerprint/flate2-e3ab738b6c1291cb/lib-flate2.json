{"rustc": 12610991425282158916, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 5382478860770802435, "deps": [[7312356825837975969, "crc32fast", false, 2524219990778376333], [7636735136738807108, "miniz_oxide", false, 5701244284660648397]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-e3ab738b6c1291cb/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}