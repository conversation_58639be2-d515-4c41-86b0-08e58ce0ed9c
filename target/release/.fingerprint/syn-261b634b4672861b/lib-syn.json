{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 11631197649975968056, "deps": [[1988483478007900009, "unicode_ident", false, 2266603835289556293], [3060637413840920116, "proc_macro2", false, 7345860360556058380], [17990358020177143287, "quote", false, 14257799989091560947]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-261b634b4672861b/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}