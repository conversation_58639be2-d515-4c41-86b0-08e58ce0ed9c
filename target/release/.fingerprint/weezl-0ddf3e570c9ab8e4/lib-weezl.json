{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2040997289075261528, "path": 9727407556035961241, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/weezl-0ddf3e570c9ab8e4/dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}