{"rustc": 12610991425282158916, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 16734212365409761697, "deps": [[40386456601120721, "percent_encoding", false, 18007525627651053105], [95042085696191081, "ipnet", false, 11579402300699854278], [264090853244900308, "sync_wrapper", false, 11155021983469502067], [784494742817713399, "tower_service", false, 17989176069091705063], [1288403060204016458, "tokio_util", false, 7561561890825282285], [1906322745568073236, "pin_project_lite", false, 13086803154891224887], [3150220818285335163, "url", false, 14139302339341293175], [3722963349756955755, "once_cell", false, 2680429131877270314], [4405182208873388884, "http", false, 8848845901429438145], [5986029879202738730, "log", false, 814433670805479473], [7414427314941361239, "hyper", false, 18321063067558850461], [7620660491849607393, "futures_core", false, 6032287127394698206], [8915503303801890683, "http_body", false, 5119778238676911845], [9689903380558560274, "serde", false, 4938397651384279630], [10229185211513642314, "mime", false, 9307769794407565231], [10629569228670356391, "futures_util", false, 13582483621617964979], [11107720164717273507, "system_configuration", false, 12777277160177305278], [12186126227181294540, "tokio_native_tls", false, 5748144593526373152], [12367227501898450486, "hyper_tls", false, 6573706473548434121], [12393800526703971956, "tokio", false, 2944440157444910603], [13763625454224483636, "h2", false, 4927453371976045784], [14564311161534545801, "encoding_rs", false, 9869343197987383523], [15367738274754116744, "serde_json", false, 11117273883172914017], [16066129441945555748, "bytes", false, 15458915817423737165], [16311359161338405624, "rustls_pemfile", false, 11186047294403548709], [16542808166767769916, "serde_urlencoded", false, 2452562448456524950], [16785601910559813697, "native_tls_crate", false, 12091704101482586297], [18066890886671768183, "base64", false, 2956230196572072070]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-e4f7eebe7bbf9f50/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}