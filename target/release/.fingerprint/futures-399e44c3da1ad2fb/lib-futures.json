{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 4691140835454490397, "deps": [[5103565458935487, "futures_io", false, 9781453494801306726], [1811549171721445101, "futures_channel", false, 15475515699512735384], [7013762810557009322, "futures_sink", false, 644585987103902007], [7620660491849607393, "futures_core", false, 6032287127394698206], [10629569228670356391, "futures_util", false, 13582483621617964979], [12779779637805422465, "futures_executor", false, 15953131106562829627], [16240732885093539806, "futures_task", false, 1817358335353630203]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-399e44c3da1ad2fb/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}