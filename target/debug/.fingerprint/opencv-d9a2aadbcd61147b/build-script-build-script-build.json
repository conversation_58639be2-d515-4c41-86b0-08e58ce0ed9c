{"rustc": 12610991425282158916, "features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "declared_features": "[\"alphamat\", \"aruco\", \"aruco_detector\", \"barcode\", \"bgsegm\", \"bioinspired\", \"calib3d\", \"ccalib\", \"clang-runtime\", \"cudaarithm\", \"cudabgsegm\", \"cudacodec\", \"cudafeatures2d\", \"cudafilters\", \"cudaimgproc\", \"cudaobjdetect\", \"cudaoptflow\", \"cudastereo\", \"cudawarping\", \"cvv\", \"default\", \"dnn\", \"dnn_superres\", \"dpm\", \"face\", \"features2d\", \"flann\", \"freetype\", \"fuzzy\", \"gapi\", \"hdf\", \"hfs\", \"highgui\", \"img_hash\", \"imgcodecs\", \"imgproc\", \"intensity_transform\", \"line_descriptor\", \"mcc\", \"ml\", \"objdetect\", \"optflow\", \"ovis\", \"phase_unwrapping\", \"photo\", \"plot\", \"quality\", \"rapid\", \"rgb\", \"rgbd\", \"saliency\", \"sfm\", \"shape\", \"stereo\", \"stitching\", \"structured_light\", \"superres\", \"surface_matching\", \"text\", \"tracking\", \"video\", \"videoio\", \"videostab\", \"viz\", \"wechat_qrcode\", \"xfeatures2d\", \"ximgproc\", \"xobjdetect\", \"xphoto\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 497799780991653260, "deps": [[3214373357989284387, "pkg_config", false, 14841581134100699207], [3722963349756955755, "once_cell", false, 3970243602726193626], [4899080583175475170, "semver", false, 7728248040418558537], [8410525223747752176, "shlex", false, 12041560257144499044], [8413798824750015470, "cc", false, 1577532454011086662], [10798972438384834726, "jobslot", false, 1252423864779699552], [11989259058781683633, "dunce", false, 9014317007529689179], [12933202132622624734, "vcpkg", false, 5698400592615022373], [15103513257332197228, "opencv_binding_generator", false, 2263825991363228107]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opencv-d9a2aadbcd61147b/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}