{"$message_type":"diagnostic","message":"unused import: `ValidationError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/dataset.rs","byte_start":131,"byte_end":146,"line_start":3,"line_end":3,"column_start":66,"column_end":81,"is_primary":true,"text":[{"text":"use crate::multimodal::utils::{MultimodalUtils, DatasetAnalysis, ValidationError};","highlight_start":66,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/dataset.rs","byte_start":129,"byte_end":146,"line_start":3,"line_end":3,"column_start":64,"column_end":81,"is_primary":true,"text":[{"text":"use crate::multimodal::utils::{MultimodalUtils, DatasetAnalysis, ValidationError};","highlight_start":64,"highlight_end":81}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ValidationError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/dataset.rs:3:66\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::multimodal::utils::{MultimodalUtils, DatasetAnalysis, ValidationError};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ApiResponse` and `StreamingResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/runner.rs","byte_start":31,"byte_end":42,"line_start":1,"line_end":1,"column_start":32,"column_end":43,"is_primary":true,"text":[{"text":"use crate::client::{ApiClient, ApiResponse, StreamingResponse};","highlight_start":32,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/runner.rs","byte_start":44,"byte_end":61,"line_start":1,"line_end":1,"column_start":45,"column_end":62,"is_primary":true,"text":[{"text":"use crate::client::{ApiClient, ApiResponse, StreamingResponse};","highlight_start":45,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/runner.rs","byte_start":29,"byte_end":61,"line_start":1,"line_end":1,"column_start":30,"column_end":62,"is_primary":true,"text":[{"text":"use crate::client::{ApiClient, ApiResponse, StreamingResponse};","highlight_start":30,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/runner.rs","byte_start":19,"byte_end":20,"line_start":1,"line_end":1,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::client::{ApiClient, ApiResponse, StreamingResponse};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/runner.rs","byte_start":61,"byte_end":62,"line_start":1,"line_end":1,"column_start":62,"column_end":63,"is_primary":true,"text":[{"text":"use crate::client::{ApiClient, ApiResponse, StreamingResponse};","highlight_start":62,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ApiResponse` and `StreamingResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/runner.rs:1:32\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::client::{ApiClient, ApiResponse, StreamingResponse};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/reporter.rs","byte_start":125,"byte_end":140,"line_start":6,"line_end":6,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/reporter.rs","byte_start":121,"byte_end":142,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":1,"highlight_end":21},{"text":"use std::time::{SystemTime, UNIX_EPOCH};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::path::Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/reporter.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::path::Path;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":1527,"byte_end":1533,"line_start":43,"line_end":43,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"        if let Some(config) = global_config {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":1527,"byte_end":1533,"line_start":43,"line_end":43,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"        if let Some(config) = global_config {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:43:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(config) = global_config {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":3272,"byte_end":3285,"line_start":89,"line_end":89,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"                let mut has_video = false;","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":3272,"byte_end":3276,"line_start":89,"line_end":89,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"                let mut has_video = false;","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:89:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let mut has_video = false;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `base_video_path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":5394,"byte_end":5409,"line_start":135,"line_end":135,"column_start":93,"column_end":108,"is_primary":true,"text":[{"text":"    pub fn validate_dataset_files(entries: &[DatasetEntry], base_image_path: Option<&Path>, base_video_path: Option<&Path>) -> Vec<ValidationError> {","highlight_start":93,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":5394,"byte_end":5409,"line_start":135,"line_end":135,"column_start":93,"column_end":108,"is_primary":true,"text":[{"text":"    pub fn validate_dataset_files(entries: &[DatasetEntry], base_image_path: Option<&Path>, base_video_path: Option<&Path>) -> Vec<ValidationError> {","highlight_start":93,"highlight_end":108}],"label":null,"suggested_replacement":"_base_video_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `base_video_path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:135:93\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mimage_path: Option<&Path>, base_video_path: Option<&Path>) -> Vec<ValidationError> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_base_video_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `api_client`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/runner.rs","byte_start":12790,"byte_end":12800,"line_start":364,"line_end":364,"column_start":54,"column_end":64,"is_primary":true,"text":[{"text":"    fn calculate_prompt_tokens(request_body: &Value, api_client: &ApiClient) -> Option<u64> {","highlight_start":54,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/runner.rs","byte_start":12790,"byte_end":12800,"line_start":364,"line_end":364,"column_start":54,"column_end":64,"is_primary":true,"text":[{"text":"    fn calculate_prompt_tokens(request_body: &Value, api_client: &ApiClient) -> Option<u64> {","highlight_start":54,"highlight_end":64}],"label":null,"suggested_replacement":"_api_client","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `api_client`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/runner.rs:364:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mtokens(request_body: &Value, api_client: &ApiClient) -> Option<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_api_client`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `resolve_video_path` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/config.rs","byte_start":6455,"byte_end":6466,"line_start":228,"line_end":228,"column_start":1,"column_end":12,"is_primary":false,"text":[{"text":"impl Config {","highlight_start":1,"highlight_end":12}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/config.rs","byte_start":8076,"byte_end":8094,"line_start":280,"line_end":280,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn resolve_video_path(&self, path: &str) -> PathBuf {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `resolve_video_path` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config.rs:280:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m228\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Config {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn resolve_video_path(&self, path: &str) -> PathBuf {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `headers` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/client.rs","byte_start":6261,"byte_end":6272,"line_start":187,"line_end":187,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct ApiResponse {","highlight_start":12,"highlight_end":23}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":6428,"byte_end":6435,"line_start":193,"line_end":193,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    pub headers: reqwest::header::HeaderMap,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ApiResponse` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `headers` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/client.rs:193:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ApiResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub headers: reqwest::header::HeaderMap,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ApiResponse` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `get_completion_text` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/client.rs","byte_start":6468,"byte_end":6484,"line_start":196,"line_end":196,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl ApiResponse {","highlight_start":1,"highlight_end":17}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":6544,"byte_end":6563,"line_start":198,"line_end":198,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn get_completion_text(&self) -> Option<String> {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `get_completion_text` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/client.rs:198:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ApiResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Extract completion text from response\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_completion_text(&self) -> Option<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `headers` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/client.rs","byte_start":7311,"byte_end":7328,"line_start":221,"line_end":221,"column_start":12,"column_end":29,"is_primary":false,"text":[{"text":"pub struct StreamingResponse {","highlight_start":12,"highlight_end":29}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":7567,"byte_end":7574,"line_start":229,"line_end":229,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    pub headers: reqwest::header::HeaderMap,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`StreamingResponse` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `headers` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/client.rs:229:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct StreamingResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub headers: reqwest::header::HeaderMap,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `StreamingResponse` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `generation_time` and `get_completion_text` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/client.rs","byte_start":7607,"byte_end":7629,"line_start":232,"line_end":232,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl StreamingResponse {","highlight_start":1,"highlight_end":23}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":8092,"byte_end":8107,"line_start":246,"line_end":246,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn generation_time(&self) -> Option<Duration> {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":8375,"byte_end":8394,"line_start":254,"line_end":254,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn get_completion_text(&self) -> String {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `generation_time` and `get_completion_text` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/client.rs:246:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl StreamingResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m246\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn generation_time(&self) -> Option<Duration> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_completion_text(&self) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `timestamp` and `data` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/client.rs","byte_start":8944,"byte_end":8955,"line_start":274,"line_end":274,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct StreamChunk {","highlight_start":12,"highlight_end":23}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":8966,"byte_end":8975,"line_start":275,"line_end":275,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub timestamp: Instant,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/client.rs","byte_start":8994,"byte_end":8998,"line_start":276,"line_end":276,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub data: Value,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`StreamChunk` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `timestamp` and `data` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/client.rs:275:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct StreamChunk {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub timestamp: Instant,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub data: Value,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `StreamChunk` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `text_only_entries`, `multimodal_entries`, `total_images`, `total_videos`, and `average_expected_tokens` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/dataset.rs","byte_start":7299,"byte_end":7316,"line_start":191,"line_end":191,"column_start":12,"column_end":29,"is_primary":false,"text":[{"text":"pub struct DatasetStatistics {","highlight_start":12,"highlight_end":29}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7388,"byte_end":7405,"line_start":194,"line_end":194,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    pub text_only_entries: usize,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7422,"byte_end":7440,"line_start":195,"line_end":195,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub multimodal_entries: usize,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7457,"byte_end":7469,"line_start":196,"line_end":196,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub total_images: u64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7484,"byte_end":7496,"line_start":197,"line_end":197,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub total_videos: u64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7511,"byte_end":7534,"line_start":198,"line_end":198,"column_start":9,"column_end":32,"is_primary":true,"text":[{"text":"    pub average_expected_tokens: f64,","highlight_start":9,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`DatasetStatistics` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `text_only_entries`, `multimodal_entries`, `total_images`, `total_videos`, and `average_expected_tokens` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/dataset.rs:194:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DatasetStatistics {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub text_only_entries: usize,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub multimodal_entries: usize,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub total_images: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub total_videos: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub average_expected_tokens: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `DatasetStatistics` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `repetition_factor` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/dataset.rs","byte_start":7573,"byte_end":7595,"line_start":202,"line_end":202,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl DatasetStatistics {","highlight_start":1,"highlight_end":23}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/dataset.rs","byte_start":7609,"byte_end":7626,"line_start":203,"line_end":203,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn repetition_factor(&self) -> f64 {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `repetition_factor` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/dataset.rs:203:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DatasetStatistics {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn repetition_factor(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `RequestStats` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":216,"byte_end":228,"line_start":9,"line_end":9,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"pub struct RequestStats {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`RequestStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `RequestStats` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:9:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct RequestStats {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `RequestStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `TestStats` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":579,"byte_end":588,"line_start":22,"line_end":22,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct TestStats {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`TestStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `TestStats` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:22:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct TestStats {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `TestStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `add_image_processing`, `add_video_processing`, `add_image_error`, `add_video_error`, `average_image_processing_time_ms`, and `average_video_processing_time_ms` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":1297,"byte_end":1317,"line_start":46,"line_end":46,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl MultimodalStats {","highlight_start":1,"highlight_end":21}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":1331,"byte_end":1351,"line_start":47,"line_end":47,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn add_image_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":1583,"byte_end":1603,"line_start":53,"line_end":53,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn add_video_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":1835,"byte_end":1850,"line_start":59,"line_end":59,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn add_image_error(&mut self) {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":1929,"byte_end":1944,"line_start":63,"line_end":63,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn add_video_error(&mut self) {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":2023,"byte_end":2055,"line_start":67,"line_end":67,"column_start":12,"column_end":44,"is_primary":true,"text":[{"text":"    pub fn average_image_processing_time_ms(&self) -> f64 {","highlight_start":12,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":2262,"byte_end":2294,"line_start":75,"line_end":75,"column_start":12,"column_end":44,"is_primary":true,"text":[{"text":"    pub fn average_video_processing_time_ms(&self) -> f64 {","highlight_start":12,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `add_image_processing`, `add_video_processing`, `add_image_error`, `add_video_error`, `average_image_processing_time_ms`, and `average_video_processing_time_ms` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:47:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MultimodalStats {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_image_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_video_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_image_error(&mut self) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_video_error(&mut self) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_image_processing_time_ms(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_video_processing_time_ms(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":2488,"byte_end":2502,"line_start":84,"line_end":84,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl TestStats {","highlight_start":1,"highlight_end":15}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":2516,"byte_end":2519,"line_start":85,"line_end":85,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":2894,"byte_end":2905,"line_start":98,"line_end":98,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn add_request(&mut self, stats: RequestStats) {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":3172,"byte_end":3178,"line_start":108,"line_end":108,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn finish(&mut self) {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":3331,"byte_end":3343,"line_start":113,"line_end":113,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn success_rate(&self) -> f64 {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":3535,"byte_end":3538,"line_start":121,"line_end":121,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn qps(&self) -> f64 {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":3751,"byte_end":3772,"line_start":129,"line_end":129,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"    pub fn average_response_time(&self) -> Duration {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":4292,"byte_end":4309,"line_start":148,"line_end":148,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn tokens_per_second(&self) -> f64 {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":4694,"byte_end":4713,"line_start":162,"line_end":162,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn total_prompt_tokens(&self) -> u64 {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":4898,"byte_end":4921,"line_start":170,"line_end":170,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn total_completion_tokens(&self) -> u64 {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":5110,"byte_end":5137,"line_start":178,"line_end":178,"column_start":12,"column_end":39,"is_primary":true,"text":[{"text":"    pub fn average_time_to_first_token(&self) -> Option<Duration> {","highlight_start":12,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:85:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m84\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TestStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_request(&mut self, stats: RequestStats) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn finish(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn success_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn qps(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_response_time(&self) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn tokens_per_second(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn total_prompt_tokens(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn total_completion_tokens(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_time_to_first_token(&self) -> Option<Duration> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `MetricsCollector` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":5699,"byte_end":5715,"line_start":199,"line_end":199,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"pub struct MetricsCollector {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `MetricsCollector` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:199:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MetricsCollector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `record_request`, `get_stats`, `finish`, `get_percentiles`, and `update_multimodal_stats` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/metrics.rs","byte_start":5798,"byte_end":5819,"line_start":204,"line_end":204,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl MetricsCollector {","highlight_start":1,"highlight_end":22}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":5833,"byte_end":5836,"line_start":205,"line_end":205,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":6235,"byte_end":6249,"line_start":216,"line_end":216,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn record_request(&self, request_stats: RequestStats) {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":6677,"byte_end":6686,"line_start":228,"line_end":228,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> TestStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":6804,"byte_end":6810,"line_start":233,"line_end":233,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    pub async fn finish(&self) {","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":6921,"byte_end":6936,"line_start":238,"line_end":238,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"    pub async fn get_percentiles(&self) -> LatencyPercentiles {","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/metrics.rs","byte_start":7373,"byte_end":7396,"line_start":251,"line_end":251,"column_start":18,"column_end":41,"is_primary":true,"text":[{"text":"    pub async fn update_multimodal_stats<F>(&self, updater: F)","highlight_start":18,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `record_request`, `get_stats`, `finish`, `get_percentiles`, and `update_multimodal_stats` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/metrics.rs:205:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MetricsCollector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m216\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn record_request(&self, request_stats: RequestStats) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m228\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> TestStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn finish(&self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_percentiles(&self) -> LatencyPercentiles {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn update_multimodal_stats<F>(&self, updater: F)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ProcessedContent` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/mod.rs","byte_start":3402,"byte_end":3418,"line_start":120,"line_end":120,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"pub struct ProcessedContent {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ProcessedContent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `ProcessedContent` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/mod.rs:120:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProcessedContent {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ProcessedContent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"enum `ProcessedData` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/mod.rs","byte_start":3516,"byte_end":3529,"line_start":126,"line_end":126,"column_start":10,"column_end":23,"is_primary":true,"text":[{"text":"pub enum ProcessedData {","highlight_start":10,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ProcessedData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: enum `ProcessedData` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/mod.rs:126:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ProcessedData {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ProcessedData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `total_video_size_bytes` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/mod.rs","byte_start":3748,"byte_end":3763,"line_start":135,"line_end":135,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct MultimodalStats {","highlight_start":12,"highlight_end":27}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":3963,"byte_end":3985,"line_start":141,"line_end":141,"column_start":9,"column_end":31,"is_primary":true,"text":[{"text":"    pub total_video_size_bytes: u64,","highlight_start":9,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MultimodalStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `total_video_size_bytes` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/mod.rs:141:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MultimodalStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub total_video_size_bytes: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `MultimodalStats` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `add_video_processing`, `add_image_error`, and `add_video_error` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/mod.rs","byte_start":4071,"byte_end":4091,"line_start":146,"line_end":146,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl MultimodalStats {","highlight_start":1,"highlight_end":21}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":4357,"byte_end":4377,"line_start":153,"line_end":153,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn add_video_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":4609,"byte_end":4624,"line_start":159,"line_end":159,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn add_image_error(&mut self) {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":4703,"byte_end":4718,"line_start":163,"line_end":163,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn add_video_error(&mut self) {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `add_video_processing`, `add_image_error`, and `add_video_error` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/mod.rs:153:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MultimodalStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_video_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_image_error(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_video_error(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `UnsupportedFormat` and `VideoError` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/mod.rs","byte_start":5348,"byte_end":5363,"line_start":186,"line_end":186,"column_start":10,"column_end":25,"is_primary":false,"text":[{"text":"pub enum MultimodalError {","highlight_start":10,"highlight_end":25}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":5654,"byte_end":5671,"line_start":197,"line_end":197,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    UnsupportedFormat(String),","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/mod.rs","byte_start":5802,"byte_end":5812,"line_start":203,"line_end":203,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    VideoError(String),","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MultimodalError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variants `UnsupportedFormat` and `VideoError` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/mod.rs:197:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum MultimodalError {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UnsupportedFormat(String),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    VideoError(String),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `MultimodalError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated functions `is_supported_format` and `get_image_dimensions` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/image.rs","byte_start":379,"byte_end":398,"line_start":15,"line_end":15,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl ImageProcessor {","highlight_start":1,"highlight_end":20}],"label":"associated functions in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/image.rs","byte_start":6482,"byte_end":6501,"line_start":167,"line_end":167,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn is_supported_format(path: &Path) -> bool {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/image.rs","byte_start":6823,"byte_end":6843,"line_start":175,"line_end":175,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn get_image_dimensions(path: &Path) -> Result<(u32, u32)> {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated functions `is_supported_format` and `get_image_dimensions` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/image.rs:167:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ImageProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated functions in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_supported_format(path: &Path) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_image_dimensions(path: &Path) -> Result<(u32, u32)> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `process_video_path`, `clear_cache`, `validate_entry`, `validate_message`, and `validate_content_type` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/request.rs","byte_start":559,"byte_end":588,"line_start":22,"line_end":22,"column_start":1,"column_end":30,"is_primary":false,"text":[{"text":"impl MultimodalRequestBuilder {","highlight_start":1,"highlight_end":30}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/request.rs","byte_start":8082,"byte_end":8100,"line_start":227,"line_end":227,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"    async fn process_video_path(&self, _path: &str, _options: &Option<()>) -> Result<Vec<String>> {","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/request.rs","byte_start":8531,"byte_end":8542,"line_start":239,"line_end":239,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn clear_cache(&self) {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/request.rs","byte_start":8677,"byte_end":8691,"line_start":245,"line_end":245,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn validate_entry(&self, entry: &DatasetEntry) -> Result<()> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/request.rs","byte_start":9491,"byte_end":9507,"line_start":270,"line_end":270,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    fn validate_message(&self, message: &Message) -> Result<()> {","highlight_start":8,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/request.rs","byte_start":9902,"byte_end":9923,"line_start":283,"line_end":283,"column_start":8,"column_end":29,"is_primary":true,"text":[{"text":"    fn validate_content_type(&self, content: &ContentType) -> Result<()> {","highlight_start":8,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `process_video_path`, `clear_cache`, `validate_entry`, `validate_message`, and `validate_content_type` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/request.rs:227:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MultimodalRequestBuilder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn process_video_path(&self, _path: &str, _options: &Option<()>) -> Result<V\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn clear_cache(&self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m245\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn validate_entry(&self, entry: &DatasetEntry) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn validate_message(&self, message: &Message) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn validate_content_type(&self, content: &ContentType) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `convert_text_to_multimodal` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":220,"byte_end":240,"line_start":9,"line_end":9,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl MultimodalUtils {","highlight_start":1,"highlight_end":21}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":8076,"byte_end":8102,"line_start":183,"line_end":183,"column_start":12,"column_end":38,"is_primary":true,"text":[{"text":"    pub fn convert_text_to_multimodal(text_entries: &[String]) -> Vec<DatasetEntry> {","highlight_start":12,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `convert_text_to_multimodal` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:183:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MultimodalUtils {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m183\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn convert_text_to_multimodal(text_entries: &[String]) -> Vec<DatasetEntry> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `message_index`, `content_index`, `error_type`, and `path` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":10398,"byte_end":10413,"line_start":255,"line_end":255,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct ValidationError {","highlight_start":12,"highlight_end":27}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10452,"byte_end":10465,"line_start":257,"line_end":257,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub message_index: Option<usize>,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10490,"byte_end":10503,"line_start":258,"line_end":258,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    pub content_index: Option<usize>,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10528,"byte_end":10538,"line_start":259,"line_end":259,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub error_type: ValidationErrorType,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10569,"byte_end":10573,"line_start":260,"line_end":260,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub path: Option<std::path::PathBuf>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ValidationError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `message_index`, `content_index`, `error_type`, and `path` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:257:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ValidationError {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub entry_index: usize,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub message_index: Option<usize>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub content_index: Option<usize>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub error_type: ValidationErrorType,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub path: Option<std::path::PathBuf>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ValidationError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `UnsupportedFormat` and `InvalidPath` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":10657,"byte_end":10676,"line_start":265,"line_end":265,"column_start":10,"column_end":29,"is_primary":false,"text":[{"text":"pub enum ValidationErrorType {","highlight_start":10,"highlight_end":29}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10701,"byte_end":10718,"line_start":267,"line_end":267,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    UnsupportedFormat,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10724,"byte_end":10735,"line_start":268,"line_end":268,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    InvalidPath,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ValidationErrorType` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variants `UnsupportedFormat` and `InvalidPath` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:267:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ValidationErrorType {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    FileNotFound,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UnsupportedFormat,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    InvalidPath,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ValidationErrorType` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `estimated_image_processing_ms` and `estimated_video_processing_ms` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/utils.rs","byte_start":10768,"byte_end":10790,"line_start":272,"line_end":272,"column_start":12,"column_end":34,"is_primary":false,"text":[{"text":"pub struct ProcessingTimeEstimate {","highlight_start":12,"highlight_end":34}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10834,"byte_end":10863,"line_start":274,"line_end":274,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"    pub estimated_image_processing_ms: u64,","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/utils.rs","byte_start":10878,"byte_end":10907,"line_start":275,"line_end":275,"column_start":9,"column_end":38,"is_primary":true,"text":[{"text":"    pub estimated_video_processing_ms: u64,","highlight_start":9,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ProcessingTimeEstimate` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `estimated_image_processing_ms` and `estimated_video_processing_ms` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/utils.rs:274:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProcessingTimeEstimate {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub estimated_total_ms: u64,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub estimated_image_processing_ms: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub estimated_video_processing_ms: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ProcessingTimeEstimate` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `VideoProcessor` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/video.rs","byte_start":264,"byte_end":278,"line_start":8,"line_end":8,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"pub struct VideoProcessor {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`VideoProcessor` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `VideoProcessor` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/video.rs:8:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct VideoProcessor {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `VideoProcessor` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `process_video_file`, `extract_frames`, `extract_frames_ffmpeg`, `is_available`, and `is_supported_format` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/multimodal/video.rs","byte_start":328,"byte_end":347,"line_start":13,"line_end":13,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl VideoProcessor {","highlight_start":1,"highlight_end":20}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":361,"byte_end":364,"line_start":14,"line_end":14,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(max_frames: u32, frame_quality: u8) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":588,"byte_end":606,"line_start":22,"line_end":22,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn process_video_file(&self, path: &Path, options: &Option<VideoOptions>) -> Result<(Vec<String>, u64, u64)> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":1254,"byte_end":1268,"line_start":41,"line_end":41,"column_start":8,"column_end":22,"is_primary":true,"text":[{"text":"    fn extract_frames(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Vec<String>> {","highlight_start":8,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":6077,"byte_end":6098,"line_start":163,"line_end":163,"column_start":8,"column_end":29,"is_primary":true,"text":[{"text":"    fn extract_frames_ffmpeg(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Vec<String>> {","highlight_start":8,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":8214,"byte_end":8226,"line_start":218,"line_end":218,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn is_available() -> bool {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multimodal/video.rs","byte_start":8619,"byte_end":8638,"line_start":233,"line_end":233,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn is_supported_format(path: &Path) -> bool {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `process_video_file`, `extract_frames`, `extract_frames_ffmpeg`, `is_available`, and `is_supported_format` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multimodal/video.rs:14:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VideoProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(max_frames: u32, frame_quality: u8) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn process_video_file(&self, path: &Path, options: &Option<VideoOptions>) -> R\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn extract_frames(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Ve\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn extract_frames_ffmpeg(&self, path: &Path, options: &Option<VideoOptions>) -> Re\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_available() -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_supported_format(path: &Path) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `model_name` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/tokenizer.rs","byte_start":114,"byte_end":123,"line_start":5,"line_end":5,"column_start":11,"column_end":20,"is_primary":false,"text":[{"text":"pub trait Tokenizer: Send + Sync {","highlight_start":11,"highlight_end":20}],"label":"method in this trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tokenizer.rs","byte_start":296,"byte_end":306,"line_start":10,"line_end":10,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"    fn model_name(&self) -> &str;","highlight_start":8,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `model_name` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tokenizer.rs:10:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Tokenizer: Send + Sync {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn model_name(&self) -> &str;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `count_completion_tokens` and `model_name` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/tokenizer.rs","byte_start":4360,"byte_end":4377,"line_start":150,"line_end":150,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl TokenCounter {","highlight_start":1,"highlight_end":18}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tokenizer.rs","byte_start":4987,"byte_end":5010,"line_start":168,"line_end":168,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn count_completion_tokens(&self, completion: &str) -> Result<usize> {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tokenizer.rs","byte_start":5163,"byte_end":5173,"line_start":173,"line_end":173,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn model_name(&self) -> &str {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `count_completion_tokens` and `model_name` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tokenizer.rs:168:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TokenCounter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count_completion_tokens(&self, completion: &str) -> Result<usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn model_name(&self) -> &str {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"36 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 36 warnings emitted\u001b[0m\n\n"}
