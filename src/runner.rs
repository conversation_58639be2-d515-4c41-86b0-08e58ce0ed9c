use crate::client::{ApiClient, ApiResponse, StreamingResponse};
use crate::config::Config;
use crate::multimodal::{DatasetEntry, MultimodalStats};
use crate::multimodal::request::MultimodalRequestBuilder;
use crate::tokenizer::TokenCounter;
use anyhow::Result;
use serde_json::Value;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, Semaphore};
use tokio::time::sleep;
use tracing::{info, warn, error, debug};

/// Statistics for a single request
#[derive(Debug, Clone)]
pub struct RequestStats {
    pub success: bool,
    pub response_time: Duration,
    pub status_code: u16,
    pub prompt_tokens: Option<u64>,
    pub completion_tokens: Option<u64>,
    pub total_tokens: Option<u64>,
    pub time_to_first_token: Option<Duration>,
    pub error_message: Option<String>,
}

/// Overall test statistics
#[derive(Debug, Clone)]
pub struct TestStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub total_response_time: Duration,
    pub min_response_time: Duration,
    pub max_response_time: Duration,
    pub total_tokens: u64,
    pub total_prompt_tokens: u64,
    pub total_completion_tokens: u64,
    pub test_duration: Duration,
    pub multimodal_stats: MultimodalStats,
    pub request_stats: Vec<RequestStats>,
}

impl TestStats {
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            total_response_time: Duration::ZERO,
            min_response_time: Duration::MAX,
            max_response_time: Duration::ZERO,
            total_tokens: 0,
            total_prompt_tokens: 0,
            total_completion_tokens: 0,
            test_duration: Duration::ZERO,
            multimodal_stats: MultimodalStats::default(),
            request_stats: Vec::new(),
        }
    }

    pub fn add_request(&mut self, stats: RequestStats) {
        self.total_requests += 1;
        
        if stats.success {
            self.successful_requests += 1;
        } else {
            self.failed_requests += 1;
        }

        self.total_response_time += stats.response_time;
        self.min_response_time = self.min_response_time.min(stats.response_time);
        self.max_response_time = self.max_response_time.max(stats.response_time);

        if let Some(tokens) = stats.total_tokens {
            self.total_tokens += tokens;
        }
        if let Some(prompt_tokens) = stats.prompt_tokens {
            self.total_prompt_tokens += prompt_tokens;
        }
        if let Some(completion_tokens) = stats.completion_tokens {
            self.total_completion_tokens += completion_tokens;
        }

        self.request_stats.push(stats);
    }

    pub fn average_response_time(&self) -> Duration {
        if self.total_requests > 0 {
            self.total_response_time / self.total_requests as u32
        } else {
            Duration::ZERO
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_requests > 0 {
            self.successful_requests as f64 / self.total_requests as f64
        } else {
            0.0
        }
    }

    pub fn qps(&self) -> f64 {
        if self.test_duration.as_secs_f64() > 0.0 {
            self.total_requests as f64 / self.test_duration.as_secs_f64()
        } else {
            0.0
        }
    }

    pub fn tokens_per_second(&self) -> f64 {
        if self.test_duration.as_secs_f64() > 0.0 {
            self.total_tokens as f64 / self.test_duration.as_secs_f64()
        } else {
            0.0
        }
    }

    /// Calculate average time to first token for streaming requests
    pub fn average_time_to_first_token(&self) -> Option<Duration> {
        let ttft_times: Vec<Duration> = self.request_stats
            .iter()
            .filter(|s| s.success)
            .filter_map(|s| s.time_to_first_token)
            .collect();

        if ttft_times.is_empty() {
            return None;
        }

        let total_ms: u64 = ttft_times
            .iter()
            .map(|d| d.as_millis() as u64)
            .sum();

        Some(Duration::from_millis(total_ms / ttft_times.len() as u64))
    }
}

/// Stress test runner
pub struct StressTestRunner {
    config: Config,
    api_client: ApiClient,
    request_builder: Option<MultimodalRequestBuilder>,
}

impl StressTestRunner {
    pub fn new(
        config: Config,
        api_client: ApiClient,
        request_builder: Option<MultimodalRequestBuilder>,
    ) -> Self {
        Self {
            config,
            api_client,
            request_builder,
        }
    }

    /// Run the stress test
    pub async fn run(&self, entries: Vec<DatasetEntry>) -> Result<TestStats> {
        let start_time = Instant::now();
        let stats = Arc::new(Mutex::new(TestStats::new()));
        
        info!("Starting stress test with {} entries", entries.len());
        info!("Concurrency: {}, Total requests: {}", self.config.concurrency, self.config.total_requests);

        // Create semaphore for concurrency control
        let semaphore = Arc::new(Semaphore::new(self.config.concurrency as usize));
        
        // Create tasks for all requests
        let mut tasks = Vec::new();
        
        for i in 0..self.config.total_requests {
            let entry = entries[i as usize % entries.len()].clone();
            let semaphore = semaphore.clone();
            let stats = stats.clone();
            let api_client = self.api_client.clone();
            let request_builder = self.request_builder.clone();
            let config = self.config.clone();
            
            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                let request_stats = Self::execute_single_request(
                    &api_client,
                    &request_builder,
                    &config,
                    &entry,
                    i + 1,
                ).await;
                
                let mut stats_guard = stats.lock().await;
                stats_guard.add_request(request_stats);
                
                // Progress reporting
                if (i + 1) % 10 == 0 || i + 1 == config.total_requests {
                    let progress = (i + 1) as f64 / config.total_requests as f64 * 100.0;
                    info!("Progress: {:.1}% ({}/{})", progress, i + 1, config.total_requests);
                }
            });
            
            tasks.push(task);
            
            // Small delay to avoid overwhelming the server
            if i > 0 && i % self.config.concurrency == 0 {
                sleep(Duration::from_millis(10)).await;
            }
        }

        // Wait for all tasks to complete
        for task in tasks {
            if let Err(e) = task.await {
                error!("Task failed: {}", e);
            }
        }

        let mut final_stats = stats.lock().await.clone();
        final_stats.test_duration = start_time.elapsed();

        // Get multimodal stats if available
        if let Some(builder) = &self.request_builder {
            final_stats.multimodal_stats = builder.get_stats().await;
        }

        info!("Stress test completed in {:.2}s", final_stats.test_duration.as_secs_f64());
        info!("Success rate: {:.1}%", final_stats.success_rate() * 100.0);
        info!("Average response time: {:.2}ms", final_stats.average_response_time().as_millis());
        info!("QPS: {:.2}", final_stats.qps());

        Ok(final_stats)
    }

    /// Execute a single request
    async fn execute_single_request(
        api_client: &ApiClient,
        request_builder: &Option<MultimodalRequestBuilder>,
        config: &Config,
        entry: &DatasetEntry,
        request_id: u32,
    ) -> RequestStats {
        debug!("Executing request {}", request_id);

        // Build request
        let request_body = match request_builder {
            Some(builder) => {
                match builder.build_request(entry).await {
                    Ok(body) => body,
                    Err(e) => {
                        warn!("Failed to build request {}: {}", request_id, e);
                        return RequestStats {
                            success: false,
                            response_time: Duration::ZERO,
                            status_code: 0,
                            prompt_tokens: None,
                            completion_tokens: None,
                            total_tokens: None,
                            time_to_first_token: None,
                            error_message: Some(format!("Request build error: {}", e)),
                        };
                    }
                }
            }
            None => {
                // Simple text request
                let default_prompt = "Hello, world!".to_string();
                let prompt = entry.prompt.as_ref().unwrap_or(&default_prompt);
                serde_json::json!({
                    "model": config.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": config.max_tokens,
                    "temperature": config.temperature,
                    "stream": config.stream
                })
            }
        };

        // Execute request
        if config.stream {
            Self::execute_streaming_request(api_client, request_body, request_id).await
        } else {
            Self::execute_regular_request(api_client, request_body, request_id).await
        }
    }

    /// Execute a regular (non-streaming) request
    async fn execute_regular_request(
        api_client: &ApiClient,
        request_body: Value,
        request_id: u32,
    ) -> RequestStats {
        match api_client.send_request(request_body).await {
            Ok(response) => {
                let token_usage = response.get_token_usage();
                RequestStats {
                    success: response.success,
                    response_time: response.response_time,
                    status_code: response.status_code,
                    prompt_tokens: token_usage.as_ref().map(|u| u.prompt_tokens),
                    completion_tokens: token_usage.as_ref().map(|u| u.completion_tokens),
                    total_tokens: token_usage.as_ref().map(|u| u.total_tokens),
                    time_to_first_token: None,
                    error_message: response.error,
                }
            }
            Err(e) => {
                warn!("Request {} failed: {}", request_id, e);
                RequestStats {
                    success: false,
                    response_time: Duration::ZERO,
                    status_code: 0,
                    prompt_tokens: None,
                    completion_tokens: None,
                    total_tokens: None,
                    time_to_first_token: None,
                    error_message: Some(e.to_string()),
                }
            }
        }
    }

    /// Execute a streaming request
    async fn execute_streaming_request(
        api_client: &ApiClient,
        request_body: Value,
        request_id: u32,
    ) -> RequestStats {
        match api_client.send_streaming_request(request_body.clone()).await {
            Ok(response) => {
                // Calculate prompt tokens using tokenizer
                let prompt_tokens = Self::calculate_prompt_tokens(&request_body, api_client);
                let completion_tokens = response.count_completion_tokens() as u64;
                let total_tokens = prompt_tokens.map(|p| p + completion_tokens).or(Some(completion_tokens));

                RequestStats {
                    success: response.success,
                    response_time: response.total_response_time(),
                    status_code: response.status_code,
                    prompt_tokens,
                    completion_tokens: Some(completion_tokens),
                    total_tokens,
                    time_to_first_token: response.time_to_first_token(),
                    error_message: response.error,
                }
            }
            Err(e) => {
                warn!("Streaming request {} failed: {}", request_id, e);
                RequestStats {
                    success: false,
                    response_time: Duration::ZERO,
                    status_code: 0,
                    prompt_tokens: None,
                    completion_tokens: None,
                    total_tokens: None,
                    time_to_first_token: None,
                    error_message: Some(e.to_string()),
                }
            }
        }
    }

    /// Calculate prompt tokens from request body
    fn calculate_prompt_tokens(request_body: &Value, api_client: &ApiClient) -> Option<u64> {
        // Extract model from request
        let model = request_body["model"].as_str()?;

        // Create tokenizer for the model
        let token_counter = match TokenCounter::new(model) {
            Ok(counter) => counter,
            Err(e) => {
                warn!("Failed to create tokenizer for model {}: {}", model, e);
                return None;
            }
        };

        // Extract prompt text from messages
        let messages = request_body["messages"].as_array()?;
        let mut total_prompt_text = String::new();

        for message in messages {
            if let Some(content) = message["content"].as_str() {
                total_prompt_text.push_str(content);
                total_prompt_text.push(' '); // Add space between messages
            } else if let Some(content_array) = message["content"].as_array() {
                // Handle multimodal content (array format)
                for content_item in content_array {
                    if let Some(text) = content_item["text"].as_str() {
                        total_prompt_text.push_str(text);
                        total_prompt_text.push(' ');
                    }
                }
            }
        }

        // Count tokens
        match token_counter.count_prompt_tokens(&total_prompt_text) {
            Ok(count) => Some(count as u64),
            Err(e) => {
                warn!("Failed to count prompt tokens: {}", e);
                None
            }
        }
    }
}
