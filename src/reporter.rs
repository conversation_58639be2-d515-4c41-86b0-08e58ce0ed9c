use crate::config::Config;
use crate::runner::TestStats;
use anyhow::Result;
use serde_json::{json, Value};
use std::fs;

use std::time::{SystemTime, UNIX_EPOCH};
use tracing::info;

/// Report generator for test results
pub struct Reporter {
    config: Config,
}

impl Reporter {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    /// Generate and save test report
    pub async fn generate_report(&self, stats: &TestStats) -> Result<()> {
        // Create output directory
        fs::create_dir_all(&self.config.output_dir)?;

        // Generate timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();
        
        let test_id = format!("{}", timestamp);

        // Generate JSON report
        let json_report = self.generate_json_report(&test_id, stats)?;
        let json_path = self.config.output_dir.join(format!("report_{}.json", test_id));
        fs::write(&json_path, serde_json::to_string_pretty(&json_report)?)?;

        // Generate text summary
        let text_summary = self.generate_text_summary(stats);
        let text_path = self.config.output_dir.join(format!("summary_{}.txt", test_id));
        fs::write(&text_path, text_summary)?;

        // Generate CSV for detailed analysis
        let csv_content = self.generate_csv_report(stats)?;
        let csv_path = self.config.output_dir.join(format!("details_{}.csv", test_id));
        fs::write(&csv_path, csv_content)?;

        // Generate HTML report with visualizations
        let html_content = self.generate_html_report(&test_id, stats)?;
        let html_path = self.config.output_dir.join(format!("report_{}.html", test_id));
        fs::write(&html_path, html_content)?;

        info!("Reports generated:");
        info!("  JSON report: {}", json_path.display());
        info!("  Text summary: {}", text_path.display());
        info!("  CSV details: {}", csv_path.display());
        info!("  HTML report: {}", html_path.display());

        // Print summary to console
        println!("\n{}", self.generate_console_summary(stats));

        Ok(())
    }

    /// Generate JSON report
    fn generate_json_report(&self, test_id: &str, stats: &TestStats) -> Result<Value> {
        let mut response_times: Vec<u64> = stats.request_stats
            .iter()
            .map(|s| s.response_time.as_millis() as u64)
            .collect();
        response_times.sort();

        let percentiles = if !response_times.is_empty() {
            json!({
                "p50": self.percentile(&response_times, 50),
                "p90": self.percentile(&response_times, 90),
                "p95": self.percentile(&response_times, 95),
                "p99": self.percentile(&response_times, 99)
            })
        } else {
            json!({
                "p50": 0,
                "p90": 0,
                "p95": 0,
                "p99": 0
            })
        };

        let report = json!({
            "test_id": test_id,
            "test_config": {
                "model": self.config.model,
                "api_base": self.config.url,
                "concurrency": self.config.concurrency,
                "total_requests": self.config.total_requests,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "stream": self.config.stream,
                "multimodal": self.config.multimodal
            },
            "summary": {
                "total_requests": stats.total_requests,
                "successful_requests": stats.successful_requests,
                "failed_requests": stats.failed_requests,
                "success_rate": stats.success_rate(),
                "test_duration_seconds": stats.test_duration.as_secs_f64(),
                "qps": stats.qps(),
                "tokens_per_second": stats.tokens_per_second()
            },
            "latency_metrics": {
                "average_ms": stats.average_response_time().as_millis(),
                "min_ms": stats.min_response_time.as_millis(),
                "max_ms": stats.max_response_time.as_millis(),
                "percentiles_ms": percentiles
            },
            "token_metrics": {
                "total_tokens": stats.total_tokens,
                "total_prompt_tokens": stats.total_prompt_tokens,
                "total_completion_tokens": stats.total_completion_tokens,
                "average_tokens_per_request": if stats.successful_requests > 0 {
                    stats.total_tokens as f64 / stats.successful_requests as f64
                } else {
                    0.0
                },
                "average_time_to_first_token_ms": stats.average_time_to_first_token()
                    .map(|d| d.as_millis() as u64)
                    .unwrap_or(0)
            },
            "multimodal_metrics": {
                "images_processed": stats.multimodal_stats.images_processed,
                "videos_processed": stats.multimodal_stats.videos_processed,
                "average_image_processing_time_ms": stats.multimodal_stats.average_image_processing_time_ms(),
                "average_video_processing_time_ms": stats.multimodal_stats.average_video_processing_time_ms(),
                "total_image_size_bytes": stats.multimodal_stats.total_image_size_bytes,
                "image_processing_errors": stats.multimodal_stats.image_processing_errors,
                "video_processing_errors": stats.multimodal_stats.video_processing_errors
            },
            "errors": self.collect_errors(stats),
            "timestamp": test_id
        });

        Ok(report)
    }

    /// Generate text summary
    fn generate_text_summary(&self, stats: &TestStats) -> String {
        format!(
            r#"Stress Test Summary
==================

Test Configuration:
- Model: {}
- API Base: {}
- Concurrency: {}
- Total Requests: {}
- Max Tokens: {}
- Temperature: {}
- Stream: {}
- Multimodal: {}

Results:
- Total Requests: {}
- Successful: {}
- Failed: {}
- Success Rate: {:.1}%
- Test Duration: {:.2}s
- QPS: {:.2}
- Tokens/sec: {:.2}

Latency Metrics:
- Average: {:.2}ms
- Min: {:.2}ms
- Max: {:.2}ms

Token Metrics:
- Total Tokens: {}
- Prompt Tokens: {}
- Completion Tokens: {}
- Avg Tokens/Request: {:.1}

Multimodal Metrics:
- Images Processed: {}
- Videos Processed: {}
- Avg Image Processing: {:.2}ms
- Image Processing Errors: {}
"#,
            self.config.model,
            self.config.url,
            self.config.concurrency,
            self.config.total_requests,
            self.config.max_tokens,
            self.config.temperature,
            self.config.stream,
            self.config.multimodal,
            stats.total_requests,
            stats.successful_requests,
            stats.failed_requests,
            stats.success_rate() * 100.0,
            stats.test_duration.as_secs_f64(),
            stats.qps(),
            stats.tokens_per_second(),
            stats.average_response_time().as_millis(),
            stats.min_response_time.as_millis(),
            stats.max_response_time.as_millis(),
            stats.total_tokens,
            stats.total_prompt_tokens,
            stats.total_completion_tokens,
            if stats.successful_requests > 0 {
                stats.total_tokens as f64 / stats.successful_requests as f64
            } else {
                0.0
            },
            stats.multimodal_stats.images_processed,
            stats.multimodal_stats.videos_processed,
            stats.multimodal_stats.average_image_processing_time_ms(),
            stats.multimodal_stats.image_processing_errors
        )
    }

    /// Generate console summary
    fn generate_console_summary(&self, stats: &TestStats) -> String {
        format!(
            r#"📊 Test Results Summary
=======================
✅ Success Rate: {:.1}% ({}/{})
⏱️  Average Latency: {:.2}ms
🚀 QPS: {:.2}
🔤 Tokens/sec: {:.2}
⏰ Duration: {:.2}s
{}"#,
            stats.success_rate() * 100.0,
            stats.successful_requests,
            stats.total_requests,
            stats.average_response_time().as_millis(),
            stats.qps(),
            stats.tokens_per_second(),
            stats.test_duration.as_secs_f64(),
            if stats.multimodal_stats.images_processed > 0 {
                format!("🖼️  Images Processed: {}", stats.multimodal_stats.images_processed)
            } else {
                String::new()
            }
        )
    }

    /// Generate CSV report for detailed analysis
    fn generate_csv_report(&self, stats: &TestStats) -> Result<String> {
        let mut csv = String::new();
        csv.push_str("request_id,success,response_time_ms,status_code,prompt_tokens,completion_tokens,total_tokens,time_to_first_token_ms,error\n");

        for (i, stat) in stats.request_stats.iter().enumerate() {
            csv.push_str(&format!(
                "{},{},{},{},{},{},{},{},{}\n",
                i + 1,
                stat.success,
                stat.response_time.as_millis(),
                stat.status_code,
                stat.prompt_tokens.unwrap_or(0),
                stat.completion_tokens.unwrap_or(0),
                stat.total_tokens.unwrap_or(0),
                stat.time_to_first_token.map(|d| d.as_millis()).unwrap_or(0),
                stat.error_message.as_deref().unwrap_or("")
            ));
        }

        Ok(csv)
    }

    /// Calculate percentile
    fn percentile(&self, sorted_values: &[u64], percentile: u8) -> u64 {
        if sorted_values.is_empty() {
            return 0;
        }

        let index = (percentile as f64 / 100.0 * (sorted_values.len() - 1) as f64).round() as usize;
        sorted_values[index.min(sorted_values.len() - 1)]
    }

    /// Collect error information
    fn collect_errors(&self, stats: &TestStats) -> Value {
        let mut error_counts = std::collections::HashMap::new();
        let mut error_examples = Vec::new();

        for stat in &stats.request_stats {
            if !stat.success {
                if let Some(error) = &stat.error_message {
                    *error_counts.entry(error.clone()).or_insert(0) += 1;
                    if error_examples.len() < 10 {
                        error_examples.push(json!({
                            "error": error,
                            "status_code": stat.status_code,
                            "response_time_ms": stat.response_time.as_millis()
                        }));
                    }
                }
            }
        }

        json!({
            "error_counts": error_counts,
            "error_examples": error_examples
        })
    }
}
