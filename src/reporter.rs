use crate::config::Config;
use crate::runner::TestStats;
use anyhow::Result;
use serde_json::{json, Value};
use std::fs;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::info;

/// Report generator for test results
pub struct Reporter {
    config: Config,
}

impl Reporter {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    /// Generate and save test report
    pub async fn generate_report(&self, stats: &TestStats) -> Result<()> {
        // Create output directory
        fs::create_dir_all(&self.config.output_dir)?;

        // Generate timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        let test_id = format!("{}", timestamp);

        // Generate comprehensive JSON report (single source of truth)
        let json_report = self.generate_json_report(&test_id, stats)?;
        let json_path = self.config.output_dir.join(format!("report_{}.json", test_id));
        fs::write(&json_path, serde_json::to_string_pretty(&json_report)?)?;

        // Generate other formats based on JSON data
        let text_summary = self.generate_text_summary(&json_report);
        let text_path = self.config.output_dir.join(format!("summary_{}.txt", test_id));
        fs::write(&text_path, text_summary)?;

        let csv_content = self.generate_csv_report(stats)?;
        let csv_path = self.config.output_dir.join(format!("details_{}.csv", test_id));
        fs::write(&csv_path, csv_content)?;

        let html_content = self.generate_html_report(&json_report)?;
        let html_path = self.config.output_dir.join(format!("report_{}.html", test_id));
        fs::write(&html_path, html_content)?;

        info!("Reports generated:");
        info!("  JSON report: {}", json_path.display());
        info!("  Text summary: {}", text_path.display());
        info!("  CSV details: {}", csv_path.display());
        info!("  HTML report: {}", html_path.display());

        // Print summary to console
        println!("\n{}", self.generate_console_summary(stats));

        Ok(())
    }

    /// Generate comprehensive JSON report (single source of truth)
    fn generate_json_report(&self, test_id: &str, stats: &TestStats) -> Result<Value> {
        let mut response_times: Vec<u64> = stats.request_stats
            .iter()
            .map(|s| s.response_time.as_millis() as u64)
            .collect();
        response_times.sort();

        // Calculate percentiles
        let percentiles = if !response_times.is_empty() {
            json!({
                "p50": self.percentile(&response_times, 50),
                "p90": self.percentile(&response_times, 90),
                "p95": self.percentile(&response_times, 95),
                "p99": self.percentile(&response_times, 99)
            })
        } else {
            json!({
                "p50": 0,
                "p90": 0,
                "p95": 0,
                "p99": 0
            })
        };

        // Generate histogram data for visualization
        let histogram_data = self.generate_histogram_data(&response_times);

        // Calculate TTFT metrics
        let ttft_times: Vec<u64> = stats.request_stats
            .iter()
            .filter_map(|s| s.time_to_first_token.map(|t| t.as_millis() as u64))
            .collect();

        let ttft_metrics = if !ttft_times.is_empty() {
            let avg_ttft = ttft_times.iter().sum::<u64>() as f64 / ttft_times.len() as f64;
            json!({
                "average_ms": avg_ttft,
                "min_ms": ttft_times.iter().min().unwrap_or(&0),
                "max_ms": ttft_times.iter().max().unwrap_or(&0),
                "count": ttft_times.len()
            })
        } else {
            json!({
                "average_ms": 0.0,
                "min_ms": 0,
                "max_ms": 0,
                "count": 0
            })
        };

        let report = json!({
            "test_id": test_id,
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "test_config": {
                "model": self.config.model,
                "url": self.config.url,
                "concurrency": self.config.concurrency,
                "total_requests": self.config.total_requests,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "stream": self.config.stream,
                "timeout": self.config.timeout,
                "multimodal": self.config.multimodal,
                "dataset": self.config.dataset.as_ref().map(|p| p.to_string_lossy().to_string())
            },
            "summary": {
                "total_requests": stats.total_requests,
                "successful_requests": stats.successful_requests,
                "failed_requests": stats.failed_requests,
                "success_rate": stats.success_rate(),
                "test_duration_seconds": stats.test_duration.as_secs_f64(),
                "qps": stats.qps(),
                "tokens_per_second": stats.tokens_per_second()
            },
            "latency_metrics": {
                "average_ms": stats.average_response_time().as_millis(),
                "min_ms": stats.min_response_time.as_millis(),
                "max_ms": stats.max_response_time.as_millis(),
                "percentiles_ms": percentiles,
                "histogram": {
                    "labels": histogram_data.0,
                    "values": histogram_data.1
                }
            },
            "token_metrics": {
                "total_tokens": stats.total_tokens,
                "total_prompt_tokens": stats.total_prompt_tokens,
                "total_completion_tokens": stats.total_completion_tokens,
                "average_tokens_per_request": if stats.successful_requests > 0 {
                    stats.total_tokens as f64 / stats.successful_requests as f64
                } else {
                    0.0
                }
            },
            "ttft_metrics": ttft_metrics,
            "multimodal_metrics": {
                "images_processed": stats.multimodal_stats.images_processed,
                "videos_processed": stats.multimodal_stats.videos_processed,
                "average_image_processing_time_ms": stats.multimodal_stats.average_image_processing_time_ms(),
                "average_video_processing_time_ms": stats.multimodal_stats.average_video_processing_time_ms(),
                "total_image_size_bytes": stats.multimodal_stats.total_image_size_bytes,
                "total_video_size_bytes": stats.multimodal_stats.total_video_size_bytes,
                "image_processing_errors": stats.multimodal_stats.image_processing_errors,
                "video_processing_errors": stats.multimodal_stats.video_processing_errors
            },
            "request_details": stats.request_stats.iter().map(|req| {
                json!({
                    "success": req.success,
                    "response_time_ms": req.response_time.as_millis(),
                    "status_code": req.status_code,
                    "error_message": req.error_message,
                    "prompt_tokens": req.prompt_tokens,
                    "completion_tokens": req.completion_tokens,
                    "total_tokens": req.total_tokens,
                    "time_to_first_token_ms": req.time_to_first_token.map(|t| t.as_millis())
                })
            }).collect::<Vec<_>>(),
            "errors": self.collect_errors(stats)
        });

        Ok(report)
    }

    /// Generate text summary from JSON data
    fn generate_text_summary(&self, json_report: &Value) -> String {
        let config = &json_report["test_config"];
        let summary = &json_report["summary"];
        let latency = &json_report["latency_metrics"];
        let tokens = &json_report["token_metrics"];
        let ttft = &json_report["ttft_metrics"];
        let multimodal = &json_report["multimodal_metrics"];

        format!(
            r#"Stress Test Summary
==================

Test Configuration:
- Model: {}
- URL: {}
- Concurrency: {}
- Total Requests: {}
- Max Tokens: {}
- Temperature: {:.2}
- Stream: {}
- Timeout: {}s
- Multimodal: {}

Results:
- Total Requests: {}
- Successful: {}
- Failed: {}
- Success Rate: {:.1}%
- Test Duration: {:.2}s
- QPS: {:.2}
- Tokens/sec: {:.2}

Latency Metrics:
- Average: {:.0}ms
- Min: {}ms
- Max: {}ms
- P50: {}ms
- P90: {}ms
- P95: {}ms
- P99: {}ms

Token Metrics:
- Total Tokens: {}
- Prompt Tokens: {}
- Completion Tokens: {}
- Avg Tokens/Request: {:.1}

TTFT Metrics:
- Average TTFT: {:.0}ms
- TTFT Count: {}

Multimodal Metrics:
- Images Processed: {}
- Videos Processed: {}
- Avg Image Processing: {:.2}ms
- Image Processing Errors: {}
- Video Processing Errors: {}
"#,
            config["model"].as_str().unwrap_or(""),
            config["url"].as_str().unwrap_or(""),
            config["concurrency"].as_u64().unwrap_or(0),
            config["total_requests"].as_u64().unwrap_or(0),
            config["max_tokens"].as_u64().unwrap_or(0),
            config["temperature"].as_f64().unwrap_or(0.0),
            config["stream"].as_bool().unwrap_or(false),
            config["timeout"].as_u64().unwrap_or(0),
            config["multimodal"].as_bool().unwrap_or(false),
            summary["total_requests"].as_u64().unwrap_or(0),
            summary["successful_requests"].as_u64().unwrap_or(0),
            summary["failed_requests"].as_u64().unwrap_or(0),
            summary["success_rate"].as_f64().unwrap_or(0.0) * 100.0,
            summary["test_duration_seconds"].as_f64().unwrap_or(0.0),
            summary["qps"].as_f64().unwrap_or(0.0),
            summary["tokens_per_second"].as_f64().unwrap_or(0.0),
            latency["average_ms"].as_u64().unwrap_or(0),
            latency["min_ms"].as_u64().unwrap_or(0),
            latency["max_ms"].as_u64().unwrap_or(0),
            latency["percentiles_ms"]["p50"].as_u64().unwrap_or(0),
            latency["percentiles_ms"]["p90"].as_u64().unwrap_or(0),
            latency["percentiles_ms"]["p95"].as_u64().unwrap_or(0),
            latency["percentiles_ms"]["p99"].as_u64().unwrap_or(0),
            tokens["total_tokens"].as_u64().unwrap_or(0),
            tokens["total_prompt_tokens"].as_u64().unwrap_or(0),
            tokens["total_completion_tokens"].as_u64().unwrap_or(0),
            tokens["average_tokens_per_request"].as_f64().unwrap_or(0.0),
            ttft["average_ms"].as_f64().unwrap_or(0.0),
            ttft["count"].as_u64().unwrap_or(0),
            multimodal["images_processed"].as_u64().unwrap_or(0),
            multimodal["videos_processed"].as_u64().unwrap_or(0),
            multimodal["average_image_processing_time_ms"].as_f64().unwrap_or(0.0),
            multimodal["image_processing_errors"].as_u64().unwrap_or(0),
            multimodal["video_processing_errors"].as_u64().unwrap_or(0)
        )
    }

    /// Generate console summary
    fn generate_console_summary(&self, stats: &TestStats) -> String {
        format!(
            r#"📊 Test Results Summary
=======================
✅ Success Rate: {:.1}% ({}/{})
⏱️  Average Latency: {:.2}ms
🚀 QPS: {:.2}
🔤 Tokens/sec: {:.2}
⏰ Duration: {:.2}s
{}"#,
            stats.success_rate() * 100.0,
            stats.successful_requests,
            stats.total_requests,
            stats.average_response_time().as_millis(),
            stats.qps(),
            stats.tokens_per_second(),
            stats.test_duration.as_secs_f64(),
            if stats.multimodal_stats.images_processed > 0 {
                format!("🖼️  Images Processed: {}", stats.multimodal_stats.images_processed)
            } else {
                String::new()
            }
        )
    }

    /// Generate CSV report for detailed analysis
    fn generate_csv_report(&self, stats: &TestStats) -> Result<String> {
        let mut csv = String::new();
        csv.push_str("request_id,success,response_time_ms,status_code,prompt_tokens,completion_tokens,total_tokens,time_to_first_token_ms,error\n");

        for (i, stat) in stats.request_stats.iter().enumerate() {
            csv.push_str(&format!(
                "{},{},{},{},{},{},{},{},{}\n",
                i + 1,
                stat.success,
                stat.response_time.as_millis(),
                stat.status_code,
                stat.prompt_tokens.unwrap_or(0),
                stat.completion_tokens.unwrap_or(0),
                stat.total_tokens.unwrap_or(0),
                stat.time_to_first_token.map(|d| d.as_millis()).unwrap_or(0),
                stat.error_message.as_deref().unwrap_or("")
            ));
        }

        Ok(csv)
    }

    /// Calculate percentile
    fn percentile(&self, sorted_values: &[u64], percentile: u8) -> u64 {
        if sorted_values.is_empty() {
            return 0;
        }

        let index = (percentile as f64 / 100.0 * (sorted_values.len() - 1) as f64).round() as usize;
        sorted_values[index.min(sorted_values.len() - 1)]
    }

    /// Collect error information
    fn collect_errors(&self, stats: &TestStats) -> Value {
        let mut error_counts = std::collections::HashMap::new();
        let mut error_examples = Vec::new();

        for stat in &stats.request_stats {
            if !stat.success {
                if let Some(error) = &stat.error_message {
                    *error_counts.entry(error.clone()).or_insert(0) += 1;
                    if error_examples.len() < 10 {
                        error_examples.push(json!({
                            "error": error,
                            "status_code": stat.status_code,
                            "response_time_ms": stat.response_time.as_millis()
                        }));
                    }
                }
            }
        }

        json!({
            "error_counts": error_counts,
            "error_examples": error_examples
        })
    }

    /// Generate HTML report with embedded JSON data for dynamic rendering
    fn generate_html_report(&self, json_report: &Value) -> Result<String> {
        let html_content = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report - {}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {{
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-bg: #f8f9fa;
            --border-color: #e0e0e0;
        }}

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}

        .header {{
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }}

        .header h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}

        .header .subtitle {{
            color: #666;
            font-size: 1.1em;
        }}

        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .metric-card {{
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
            transition: transform 0.2s ease;
        }}

        .metric-card:hover {{
            transform: translateY(-2px);
        }}

        .metric-card.success {{ border-left-color: var(--success-color); }}
        .metric-card.warning {{ border-left-color: var(--warning-color); }}
        .metric-card.danger {{ border-left-color: var(--danger-color); }}

        .metric-value {{
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }}

        .metric-label {{
            color: #666;
            font-size: 1em;
            font-weight: 500;
        }}

        .chart-container {{
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        .chart-title {{
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 25px;
            color: #333;
        }}

        .section {{
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        .section h3 {{
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
        }}

        .config-table, .details-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}

        .config-table th,
        .config-table td,
        .details-table th,
        .details-table td {{
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }}

        .config-table th,
        .details-table th {{
            background: var(--light-bg);
            font-weight: 600;
            color: #555;
        }}

        .config-table td {{
            word-break: break-all;
        }}

        .config-table tr:hover,
        .details-table tr:hover {{
            background: var(--light-bg);
        }}

        .error-section {{
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }}

        .error-title {{
            color: var(--danger-color);
            font-weight: bold;
            margin-bottom: 15px;
        }}

        .tabs {{
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 20px;
        }}

        .tab {{
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 1em;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }}

        .tab.active {{
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }}

        .tab-content {{
            display: none;
        }}

        .tab-content.active {{
            display: block;
        }}

        @media (max-width: 768px) {{
            .metrics-grid {{
                grid-template-columns: 1fr;
            }}

            .container {{
                padding: 10px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Stress Test Report</h1>
            <div class="subtitle">Test ID: <span id="test-id"></span> | Generated: <span id="timestamp"></span></div>
        </div>

        <div class="metrics-grid" id="metrics-grid">
            <!-- Metrics will be populated by JavaScript -->
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 Response Time Distribution</div>
            <canvas id="histogramChart" width="400" height="200"></canvas>
        </div>

        <div class="section">
            <h3>📈 Detailed Metrics</h3>
            <div class="tabs">
                <button class="tab active" onclick="showTab('latency')">Latency</button>
                <button class="tab" onclick="showTab('tokens')">Tokens</button>
                <button class="tab" onclick="showTab('ttft')">TTFT</button>
                <button class="tab" onclick="showTab('multimodal')">Multimodal</button>
            </div>

            <div id="latency-content" class="tab-content active">
                <table class="details-table" id="latency-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="tokens-content" class="tab-content">
                <table class="details-table" id="tokens-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="ttft-content" class="tab-content">
                <table class="details-table" id="ttft-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="multimodal-content" class="tab-content">
                <table class="details-table" id="multimodal-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>
        </div>

        <div class="section">
            <h3>⚙️ Test Configuration</h3>
            <table class="config-table" id="config-table">
                <!-- Populated by JavaScript -->
            </table>
        </div>

        <div class="section" id="errors-section" style="display: none;">
            <h3>❌ Errors</h3>
            <div id="errors-content">
                <!-- Populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Embedded test data
        const testData = {};

        // Initialize the report
        document.addEventListener('DOMContentLoaded', function() {{
            initializeReport();
        }});

        function initializeReport() {{
            populateHeader();
            populateMetrics();
            populateChart();
            populateDetailedMetrics();
            populateConfiguration();
            populateErrors();
        }}

        function populateHeader() {{
            document.getElementById('test-id').textContent = testData.test_id || 'Unknown';
            document.getElementById('timestamp').textContent = new Date(testData.timestamp).toLocaleString() || 'Unknown';
        }}

        function populateMetrics() {{
            const summary = testData.summary || {{}};
            const latency = testData.latency_metrics || {{}};
            const tokens = testData.token_metrics || {{}};

            const metricsGrid = document.getElementById('metrics-grid');
            metricsGrid.innerHTML = `
                <div class="metric-card success">
                    <div class="metric-value">${{(summary.success_rate * 100).toFixed(1)}}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${{latency.average_ms || 0}}</div>
                    <div class="metric-label">Avg Response Time (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${{summary.qps?.toFixed(2) || '0.00'}}</div>
                    <div class="metric-label">Requests/sec</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${{summary.tokens_per_second?.toFixed(2) || '0.00'}}</div>
                    <div class="metric-label">Tokens/sec</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${{summary.total_requests || 0}}</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${{summary.test_duration_seconds?.toFixed(1) || '0.0'}}s</div>
                    <div class="metric-label">Duration</div>
                </div>
            `;
        }}

        function populateChart() {{
            const latency = testData.latency_metrics || {{}};
            const histogram = latency.histogram || {{ labels: [], values: [] }};

            const ctx = document.getElementById('histogramChart').getContext('2d');
            new Chart(ctx, {{
                type: 'bar',
                data: {{
                    labels: histogram.labels,
                    datasets: [{{
                        label: 'Request Count',
                        data: histogram.values,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }}]
                }},
                options: {{
                    responsive: true,
                    plugins: {{
                        legend: {{ display: false }},
                        title: {{
                            display: true,
                            text: 'Response Time Distribution (ms)'
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{ display: true, text: 'Number of Requests' }}
                        }},
                        x: {{
                            title: {{ display: true, text: 'Response Time (ms)' }}
                        }}
                    }}
                }}
            }});
        }}

        function populateDetailedMetrics() {{
            // Latency metrics
            const latency = testData.latency_metrics || {{}};
            const percentiles = latency.percentiles_ms || {{}};
            document.getElementById('latency-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Average</td><td>${{latency.average_ms || 0}} ms</td></tr>
                <tr><td>Minimum</td><td>${{latency.min_ms || 0}} ms</td></tr>
                <tr><td>Maximum</td><td>${{latency.max_ms || 0}} ms</td></tr>
                <tr><td>P50 (Median)</td><td>${{percentiles.p50 || 0}} ms</td></tr>
                <tr><td>P90</td><td>${{percentiles.p90 || 0}} ms</td></tr>
                <tr><td>P95</td><td>${{percentiles.p95 || 0}} ms</td></tr>
                <tr><td>P99</td><td>${{percentiles.p99 || 0}} ms</td></tr>
            `;

            // Token metrics
            const tokens = testData.token_metrics || {{}};
            document.getElementById('tokens-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Total Tokens</td><td>${{tokens.total_tokens || 0}}</td></tr>
                <tr><td>Prompt Tokens</td><td>${{tokens.total_prompt_tokens || 0}}</td></tr>
                <tr><td>Completion Tokens</td><td>${{tokens.total_completion_tokens || 0}}</td></tr>
                <tr><td>Avg Tokens/Request</td><td>${{tokens.average_tokens_per_request?.toFixed(1) || '0.0'}}</td></tr>
            `;

            // TTFT metrics
            const ttft = testData.ttft_metrics || {{}};
            document.getElementById('ttft-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Average TTFT</td><td>${{ttft.average_ms?.toFixed(0) || 0}} ms</td></tr>
                <tr><td>Min TTFT</td><td>${{ttft.min_ms || 0}} ms</td></tr>
                <tr><td>Max TTFT</td><td>${{ttft.max_ms || 0}} ms</td></tr>
                <tr><td>TTFT Count</td><td>${{ttft.count || 0}}</td></tr>
            `;

            // Multimodal metrics
            const multimodal = testData.multimodal_metrics || {{}};
            document.getElementById('multimodal-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Images Processed</td><td>${{multimodal.images_processed || 0}}</td></tr>
                <tr><td>Videos Processed</td><td>${{multimodal.videos_processed || 0}}</td></tr>
                <tr><td>Avg Image Processing Time</td><td>${{multimodal.average_image_processing_time_ms?.toFixed(2) || '0.00'}} ms</td></tr>
                <tr><td>Avg Video Processing Time</td><td>${{multimodal.average_video_processing_time_ms?.toFixed(2) || '0.00'}} ms</td></tr>
                <tr><td>Total Image Size</td><td>${{formatBytes(multimodal.total_image_size_bytes || 0)}}</td></tr>
                <tr><td>Total Video Size</td><td>${{formatBytes(multimodal.total_video_size_bytes || 0)}}</td></tr>
                <tr><td>Image Processing Errors</td><td>${{multimodal.image_processing_errors || 0}}</td></tr>
                <tr><td>Video Processing Errors</td><td>${{multimodal.video_processing_errors || 0}}</td></tr>
            `;
        }}

        function populateConfiguration() {{
            const config = testData.test_config || {{}};
            document.getElementById('config-table').innerHTML = `
                <tr><th>Setting</th><th>Value</th></tr>
                <tr><td>Target URL</td><td>${{config.url || 'N/A'}}</td></tr>
                <tr><td>Model</td><td>${{config.model || 'N/A'}}</td></tr>
                <tr><td>Concurrency</td><td>${{config.concurrency || 0}}</td></tr>
                <tr><td>Total Requests</td><td>${{config.total_requests || 0}}</td></tr>
                <tr><td>Max Tokens</td><td>${{config.max_tokens || 0}}</td></tr>
                <tr><td>Temperature</td><td>${{config.temperature || 0}}</td></tr>
                <tr><td>Streaming</td><td>${{config.stream ? 'Yes' : 'No'}}</td></tr>
                <tr><td>Timeout</td><td>${{config.timeout || 0}}s</td></tr>
                <tr><td>Multimodal</td><td>${{config.multimodal ? 'Yes' : 'No'}}</td></tr>
                <tr><td>Dataset</td><td>${{config.dataset || 'N/A'}}</td></tr>
            `;
        }}

        function populateErrors() {{
            const errors = testData.errors || {{}};
            const errorCounts = errors.error_counts || {{}};

            if (Object.keys(errorCounts).length > 0) {{
                document.getElementById('errors-section').style.display = 'block';
                let errorHtml = '<div class="error-section">';
                errorHtml += '<div class="error-title">Error Summary</div>';
                errorHtml += '<table class="details-table"><tr><th>Error Type</th><th>Count</th></tr>';

                for (const [error, count] of Object.entries(errorCounts)) {{
                    errorHtml += `<tr><td>${{error}}</td><td>${{count}}</td></tr>`;
                }}

                errorHtml += '</table></div>';
                document.getElementById('errors-content').innerHTML = errorHtml;
            }}
        }}

        function showTab(tabName) {{
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {{
                content.classList.remove('active');
            }});

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {{
                tab.classList.remove('active');
            }});

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }}

        function formatBytes(bytes) {{
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }}
    </script>
</body>
</html>
"#,
            json_report["test_id"].as_str().unwrap_or("unknown"),
            serde_json::to_string(json_report)?
        );

        Ok(html_content)
    }
        let histogram_data = self.generate_histogram_data(&response_times);

        let html_content = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report - {}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }}
        .header h1 {{
            color: #333;
            margin: 0;
        }}
        .header .subtitle {{
            color: #666;
            margin-top: 10px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .metric-card {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }}
        .metric-card.success {{
            border-left-color: #28a745;
        }}
        .metric-card.warning {{
            border-left-color: #ffc107;
        }}
        .metric-card.danger {{
            border-left-color: #dc3545;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .chart-container {{
            margin: 40px 0;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }}
        .config-section {{
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }}
        .config-section h3 {{
            margin-top: 0;
            color: #333;
            margin-bottom: 20px;
        }}
        .config-table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .config-table th,
        .config-table td {{
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }}
        .config-table th {{
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            width: 40%;
        }}
        .config-table td {{
            color: #333;
            word-break: break-all;
        }}
        .config-table tr:last-child th,
        .config-table tr:last-child td {{
            border-bottom: none;
        }}
        .config-table tr:hover {{
            background: #f8f9fa;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Stress Test Report</h1>
            <div class="subtitle">Test ID: {} | Generated: {}</div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card success">
                <div class="metric-value">{:.1}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.0}ms</div>
                <div class="metric-label">Avg Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.2}</div>
                <div class="metric-label">Requests/sec</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.2}</div>
                <div class="metric-label">Tokens/sec</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{}</div>
                <div class="metric-label">Total Requests</div>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 Response Time Distribution</div>
            <canvas id="histogramChart" width="400" height="200"></canvas>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P50 (Median)</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P90</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P95</div>
            </div>
            <div class="metric-card danger">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P99</div>
            </div>
        </div>

        <div class="config-section">
            <h3>⚙️ Test Configuration</h3>
            <table class="config-table">
                <tbody>
                    <tr>
                        <th>Target URL</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Model</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Concurrency</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Total Requests</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Max Tokens</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Temperature</th>
                        <td>{:.2}</td>
                    </tr>
                    <tr>
                        <th>Streaming</th>
                        <td>{}</td>
                    </tr>
                    <tr>
                        <th>Duration</th>
                        <td>{:.2}s</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Histogram Chart
        const ctx = document.getElementById('histogramChart').getContext('2d');
        const histogramChart = new Chart(ctx, {{
            type: 'bar',
            data: {{
                labels: {},
                datasets: [{{
                    label: 'Request Count',
                    data: {},
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        display: false
                    }},
                    title: {{
                        display: true,
                        text: 'Response Time Distribution (ms)'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: 'Number of Requests'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: 'Response Time (ms)'
                        }}
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"#,
            test_id,
            test_id,
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
            success_rate,
            avg_response_time,
            qps,
            stats.tokens_per_second(),
            total_count,
            p50,
            p90,
            p95,
            p99,
            self.config.url,
            self.config.model,
            self.config.concurrency,
            self.config.total_requests,
            self.config.max_tokens,
            self.config.temperature,
            self.config.stream,
            stats.test_duration.as_secs_f64(),
            serde_json::to_string(&histogram_data.0)?,
            serde_json::to_string(&histogram_data.1)?
        );

        Ok(html_content)
    }

    /// Generate histogram data for response times
    fn generate_histogram_data(&self, response_times: &[u64]) -> (Vec<String>, Vec<u64>) {
        if response_times.is_empty() {
            return (vec![], vec![]);
        }

        let min_time = *response_times.iter().min().unwrap();
        let max_time = *response_times.iter().max().unwrap();

        // Create 20 buckets
        let bucket_count = 20;
        let bucket_size = if max_time > min_time {
            (max_time - min_time) / bucket_count + 1
        } else {
            1
        };

        let mut buckets = vec![0u64; bucket_count as usize];
        let mut labels = Vec::new();

        // Generate bucket labels
        for i in 0..bucket_count {
            let start = min_time + i * bucket_size;
            let end = min_time + (i + 1) * bucket_size;
            labels.push(format!("{}-{}", start, end));
        }

        // Fill buckets
        for &time in response_times {
            let bucket_index = ((time - min_time) / bucket_size).min(bucket_count - 1) as usize;
            buckets[bucket_index] += 1;
        }

        (labels, buckets)
    }

    /// Calculate percentile from sorted response times
    fn calculate_percentile(&self, sorted_times: &[u64], percentile: f64) -> u64 {
        if sorted_times.is_empty() {
            return 0;
        }

        let index = (percentile / 100.0 * (sorted_times.len() - 1) as f64).round() as usize;
        sorted_times[index.min(sorted_times.len() - 1)]
    }
}
