use crate::config::Config;
use crate::runner::TestStats;
use anyhow::Result;
use serde_json::{json, Value};
use std::fs;

use std::time::{SystemTime, UNIX_EPOCH};
use tracing::info;

/// Report generator for test results
pub struct Reporter {
    config: Config,
}

impl Reporter {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    /// Generate and save test report
    pub async fn generate_report(&self, stats: &TestStats) -> Result<()> {
        // Create output directory
        fs::create_dir_all(&self.config.output_dir)?;

        // Generate timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();
        
        let test_id = format!("{}", timestamp);

        // Generate JSON report
        let json_report = self.generate_json_report(&test_id, stats)?;
        let json_path = self.config.output_dir.join(format!("report_{}.json", test_id));
        fs::write(&json_path, serde_json::to_string_pretty(&json_report)?)?;

        // Generate text summary
        let text_summary = self.generate_text_summary(stats);
        let text_path = self.config.output_dir.join(format!("summary_{}.txt", test_id));
        fs::write(&text_path, text_summary)?;

        // Generate CSV for detailed analysis
        let csv_content = self.generate_csv_report(stats)?;
        let csv_path = self.config.output_dir.join(format!("details_{}.csv", test_id));
        fs::write(&csv_path, csv_content)?;

        // Generate HTML report with visualizations
        let html_content = self.generate_html_report(&test_id, stats)?;
        let html_path = self.config.output_dir.join(format!("report_{}.html", test_id));
        fs::write(&html_path, html_content)?;

        info!("Reports generated:");
        info!("  JSON report: {}", json_path.display());
        info!("  Text summary: {}", text_path.display());
        info!("  CSV details: {}", csv_path.display());
        info!("  HTML report: {}", html_path.display());

        // Print summary to console
        println!("\n{}", self.generate_console_summary(stats));

        Ok(())
    }

    /// Generate JSON report
    fn generate_json_report(&self, test_id: &str, stats: &TestStats) -> Result<Value> {
        let mut response_times: Vec<u64> = stats.request_stats
            .iter()
            .map(|s| s.response_time.as_millis() as u64)
            .collect();
        response_times.sort();

        let percentiles = if !response_times.is_empty() {
            json!({
                "p50": self.percentile(&response_times, 50),
                "p90": self.percentile(&response_times, 90),
                "p95": self.percentile(&response_times, 95),
                "p99": self.percentile(&response_times, 99)
            })
        } else {
            json!({
                "p50": 0,
                "p90": 0,
                "p95": 0,
                "p99": 0
            })
        };

        let report = json!({
            "test_id": test_id,
            "test_config": {
                "model": self.config.model,
                "api_base": self.config.url,
                "concurrency": self.config.concurrency,
                "total_requests": self.config.total_requests,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "stream": self.config.stream,
                "multimodal": self.config.multimodal
            },
            "summary": {
                "total_requests": stats.total_requests,
                "successful_requests": stats.successful_requests,
                "failed_requests": stats.failed_requests,
                "success_rate": stats.success_rate(),
                "test_duration_seconds": stats.test_duration.as_secs_f64(),
                "qps": stats.qps(),
                "tokens_per_second": stats.tokens_per_second()
            },
            "latency_metrics": {
                "average_ms": stats.average_response_time().as_millis(),
                "min_ms": stats.min_response_time.as_millis(),
                "max_ms": stats.max_response_time.as_millis(),
                "percentiles_ms": percentiles
            },
            "token_metrics": {
                "total_tokens": stats.total_tokens,
                "total_prompt_tokens": stats.total_prompt_tokens,
                "total_completion_tokens": stats.total_completion_tokens,
                "average_tokens_per_request": if stats.successful_requests > 0 {
                    stats.total_tokens as f64 / stats.successful_requests as f64
                } else {
                    0.0
                },
                "average_time_to_first_token_ms": stats.average_time_to_first_token()
                    .map(|d| d.as_millis() as u64)
                    .unwrap_or(0)
            },
            "multimodal_metrics": {
                "images_processed": stats.multimodal_stats.images_processed,
                "videos_processed": stats.multimodal_stats.videos_processed,
                "average_image_processing_time_ms": stats.multimodal_stats.average_image_processing_time_ms(),
                "average_video_processing_time_ms": stats.multimodal_stats.average_video_processing_time_ms(),
                "total_image_size_bytes": stats.multimodal_stats.total_image_size_bytes,
                "image_processing_errors": stats.multimodal_stats.image_processing_errors,
                "video_processing_errors": stats.multimodal_stats.video_processing_errors
            },
            "errors": self.collect_errors(stats),
            "timestamp": test_id
        });

        Ok(report)
    }

    /// Generate text summary
    fn generate_text_summary(&self, stats: &TestStats) -> String {
        format!(
            r#"Stress Test Summary
==================

Test Configuration:
- Model: {}
- API Base: {}
- Concurrency: {}
- Total Requests: {}
- Max Tokens: {}
- Temperature: {}
- Stream: {}
- Multimodal: {}

Results:
- Total Requests: {}
- Successful: {}
- Failed: {}
- Success Rate: {:.1}%
- Test Duration: {:.2}s
- QPS: {:.2}
- Tokens/sec: {:.2}

Latency Metrics:
- Average: {:.2}ms
- Min: {:.2}ms
- Max: {:.2}ms

Token Metrics:
- Total Tokens: {}
- Prompt Tokens: {}
- Completion Tokens: {}
- Avg Tokens/Request: {:.1}

Multimodal Metrics:
- Images Processed: {}
- Videos Processed: {}
- Avg Image Processing: {:.2}ms
- Image Processing Errors: {}
"#,
            self.config.model,
            self.config.url,
            self.config.concurrency,
            self.config.total_requests,
            self.config.max_tokens,
            self.config.temperature,
            self.config.stream,
            self.config.multimodal,
            stats.total_requests,
            stats.successful_requests,
            stats.failed_requests,
            stats.success_rate() * 100.0,
            stats.test_duration.as_secs_f64(),
            stats.qps(),
            stats.tokens_per_second(),
            stats.average_response_time().as_millis(),
            stats.min_response_time.as_millis(),
            stats.max_response_time.as_millis(),
            stats.total_tokens,
            stats.total_prompt_tokens,
            stats.total_completion_tokens,
            if stats.successful_requests > 0 {
                stats.total_tokens as f64 / stats.successful_requests as f64
            } else {
                0.0
            },
            stats.multimodal_stats.images_processed,
            stats.multimodal_stats.videos_processed,
            stats.multimodal_stats.average_image_processing_time_ms(),
            stats.multimodal_stats.image_processing_errors
        )
    }

    /// Generate console summary
    fn generate_console_summary(&self, stats: &TestStats) -> String {
        format!(
            r#"📊 Test Results Summary
=======================
✅ Success Rate: {:.1}% ({}/{})
⏱️  Average Latency: {:.2}ms
🚀 QPS: {:.2}
🔤 Tokens/sec: {:.2}
⏰ Duration: {:.2}s
{}"#,
            stats.success_rate() * 100.0,
            stats.successful_requests,
            stats.total_requests,
            stats.average_response_time().as_millis(),
            stats.qps(),
            stats.tokens_per_second(),
            stats.test_duration.as_secs_f64(),
            if stats.multimodal_stats.images_processed > 0 {
                format!("🖼️  Images Processed: {}", stats.multimodal_stats.images_processed)
            } else {
                String::new()
            }
        )
    }

    /// Generate CSV report for detailed analysis
    fn generate_csv_report(&self, stats: &TestStats) -> Result<String> {
        let mut csv = String::new();
        csv.push_str("request_id,success,response_time_ms,status_code,prompt_tokens,completion_tokens,total_tokens,time_to_first_token_ms,error\n");

        for (i, stat) in stats.request_stats.iter().enumerate() {
            csv.push_str(&format!(
                "{},{},{},{},{},{},{},{},{}\n",
                i + 1,
                stat.success,
                stat.response_time.as_millis(),
                stat.status_code,
                stat.prompt_tokens.unwrap_or(0),
                stat.completion_tokens.unwrap_or(0),
                stat.total_tokens.unwrap_or(0),
                stat.time_to_first_token.map(|d| d.as_millis()).unwrap_or(0),
                stat.error_message.as_deref().unwrap_or("")
            ));
        }

        Ok(csv)
    }

    /// Calculate percentile
    fn percentile(&self, sorted_values: &[u64], percentile: u8) -> u64 {
        if sorted_values.is_empty() {
            return 0;
        }

        let index = (percentile as f64 / 100.0 * (sorted_values.len() - 1) as f64).round() as usize;
        sorted_values[index.min(sorted_values.len() - 1)]
    }

    /// Collect error information
    fn collect_errors(&self, stats: &TestStats) -> Value {
        let mut error_counts = std::collections::HashMap::new();
        let mut error_examples = Vec::new();

        for stat in &stats.request_stats {
            if !stat.success {
                if let Some(error) = &stat.error_message {
                    *error_counts.entry(error.clone()).or_insert(0) += 1;
                    if error_examples.len() < 10 {
                        error_examples.push(json!({
                            "error": error,
                            "status_code": stat.status_code,
                            "response_time_ms": stat.response_time.as_millis()
                        }));
                    }
                }
            }
        }

        json!({
            "error_counts": error_counts,
            "error_examples": error_examples
        })
    }

    /// Generate HTML report with visualizations
    fn generate_html_report(&self, test_id: &str, stats: &TestStats) -> Result<String> {
        let mut response_times: Vec<u64> = stats.request_stats
            .iter()
            .map(|stat| stat.response_time.as_millis() as u64)
            .collect();
        response_times.sort();

        let success_count = stats.request_stats.iter().filter(|s| s.success).count();
        let total_count = stats.request_stats.len();
        let success_rate = if total_count > 0 {
            (success_count as f64 / total_count as f64) * 100.0
        } else {
            0.0
        };

        let avg_response_time = if !response_times.is_empty() {
            response_times.iter().sum::<u64>() as f64 / response_times.len() as f64
        } else {
            0.0
        };

        let qps = if stats.test_duration.as_secs_f64() > 0.0 {
            total_count as f64 / stats.test_duration.as_secs_f64()
        } else {
            0.0
        };

        // Calculate percentiles
        let p50 = self.calculate_percentile(&response_times, 50.0);
        let p90 = self.calculate_percentile(&response_times, 90.0);
        let p95 = self.calculate_percentile(&response_times, 95.0);
        let p99 = self.calculate_percentile(&response_times, 99.0);

        // Generate histogram data for Chart.js
        let histogram_data = self.generate_histogram_data(&response_times);

        let html_content = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report - {}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }}
        .header h1 {{
            color: #333;
            margin: 0;
        }}
        .header .subtitle {{
            color: #666;
            margin-top: 10px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .metric-card {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }}
        .metric-card.success {{
            border-left-color: #28a745;
        }}
        .metric-card.warning {{
            border-left-color: #ffc107;
        }}
        .metric-card.danger {{
            border-left-color: #dc3545;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .chart-container {{
            margin: 40px 0;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }}
        .config-section {{
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }}
        .config-section h3 {{
            margin-top: 0;
            color: #333;
        }}
        .config-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        .config-item {{
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }}
        .config-label {{
            font-weight: 500;
            color: #555;
        }}
        .config-value {{
            color: #333;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Stress Test Report</h1>
            <div class="subtitle">Test ID: {} | Generated: {}</div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card success">
                <div class="metric-value">{:.1}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.0}ms</div>
                <div class="metric-label">Avg Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.2}</div>
                <div class="metric-label">Requests/sec</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{:.2}</div>
                <div class="metric-label">Tokens/sec</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{}</div>
                <div class="metric-label">Total Requests</div>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 Response Time Distribution</div>
            <canvas id="histogramChart" width="400" height="200"></canvas>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P50 (Median)</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P90</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P95</div>
            </div>
            <div class="metric-card danger">
                <div class="metric-value">{}ms</div>
                <div class="metric-label">P99</div>
            </div>
        </div>

        <div class="config-section">
            <h3>⚙️ Test Configuration</h3>
            <div class="config-grid">
                <div class="config-item">
                    <span class="config-label">Target URL:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Model:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Concurrency:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Total Requests:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Max Tokens:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Temperature:</span>
                    <span class="config-value">{:.2}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Streaming:</span>
                    <span class="config-value">{}</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Duration:</span>
                    <span class="config-value">{:.2}s</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Histogram Chart
        const ctx = document.getElementById('histogramChart').getContext('2d');
        const histogramChart = new Chart(ctx, {{
            type: 'bar',
            data: {{
                labels: {},
                datasets: [{{
                    label: 'Request Count',
                    data: {},
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        display: false
                    }},
                    title: {{
                        display: true,
                        text: 'Response Time Distribution (ms)'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: 'Number of Requests'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: 'Response Time (ms)'
                        }}
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"#,
            test_id,
            test_id,
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
            success_rate,
            avg_response_time,
            qps,
            stats.tokens_per_second(),
            total_count,
            p50,
            p90,
            p95,
            p99,
            self.config.url,
            self.config.model,
            self.config.concurrency,
            self.config.total_requests,
            self.config.max_tokens,
            self.config.temperature,
            self.config.stream,
            stats.test_duration.as_secs_f64(),
            serde_json::to_string(&histogram_data.0)?,
            serde_json::to_string(&histogram_data.1)?
        );

        Ok(html_content)
    }

    /// Generate histogram data for response times
    fn generate_histogram_data(&self, response_times: &[u64]) -> (Vec<String>, Vec<u64>) {
        if response_times.is_empty() {
            return (vec![], vec![]);
        }

        let min_time = *response_times.iter().min().unwrap();
        let max_time = *response_times.iter().max().unwrap();

        // Create 20 buckets
        let bucket_count = 20;
        let bucket_size = if max_time > min_time {
            (max_time - min_time) / bucket_count + 1
        } else {
            1
        };

        let mut buckets = vec![0u64; bucket_count as usize];
        let mut labels = Vec::new();

        // Generate bucket labels
        for i in 0..bucket_count {
            let start = min_time + i * bucket_size;
            let end = min_time + (i + 1) * bucket_size;
            labels.push(format!("{}-{}", start, end));
        }

        // Fill buckets
        for &time in response_times {
            let bucket_index = ((time - min_time) / bucket_size).min(bucket_count - 1) as usize;
            buckets[bucket_index] += 1;
        }

        (labels, buckets)
    }

    /// Calculate percentile from sorted response times
    fn calculate_percentile(&self, sorted_times: &[u64], percentile: f64) -> u64 {
        if sorted_times.is_empty() {
            return 0;
        }

        let index = (percentile / 100.0 * (sorted_times.len() - 1) as f64).round() as usize;
        sorted_times[index.min(sorted_times.len() - 1)]
    }
}
