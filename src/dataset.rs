use crate::config::Config;
use crate::multimodal::{DatasetEntry};
use crate::multimodal::utils::{MultimodalUtils, DatasetAnalysis};
use anyhow::{Context, Result};
use std::path::Path;
use tracing::{info, warn};

/// Dataset loader that handles both text and multimodal datasets
pub struct DatasetLoader {
    config: Config,
}

impl DatasetLoader {
    pub fn new(config: Config) -> Self {
        Self { config }
    }
    
    /// Load dataset from file or generate default entries
    pub async fn load_dataset(&self) -> Result<Vec<DatasetEntry>> {
        match &self.config.dataset {
            Some(dataset_path) => {
                info!("Loading dataset from: {}", dataset_path.display());
                self.load_from_file(dataset_path).await
            }
            None => {
                info!("No dataset specified, generating default entries");
                Ok(self.generate_default_entries())
            }
        }
    }
    
    /// Load dataset from JSONL file
    async fn load_from_file(&self, path: &Path) -> Result<Vec<DatasetEntry>> {
        if !path.exists() {
            anyhow::bail!("Dataset file not found: {}", path.display());
        }
        
        let entries = MultimodalUtils::load_dataset(path)
            .with_context(|| format!("Failed to load dataset from {}", path.display()))?;
        
        if entries.is_empty() {
            anyhow::bail!("Dataset file is empty: {}", path.display());
        }
        
        info!("Loaded {} entries from dataset", entries.len());
        
        // Analyze dataset
        let analysis = MultimodalUtils::analyze_dataset(&entries);
        self.log_dataset_analysis(&analysis);
        
        // Validate multimodal content if enabled
        if self.config.multimodal || MultimodalUtils::is_multimodal_dataset(&entries) {
            self.validate_multimodal_content(&entries).await?;
        }
        
        Ok(entries)
    }
    
    /// Generate default text entries for testing
    fn generate_default_entries(&self) -> Vec<DatasetEntry> {
        let default_prompts = vec![
            "What is the capital of France?",
            "Explain quantum computing in simple terms",
            "Write a short poem about the ocean",
            "What are the benefits of renewable energy?",
            "Describe the process of photosynthesis",
            "How does machine learning work?",
            "What is the theory of relativity?",
            "Explain the water cycle",
            "What causes climate change?",
            "How do vaccines work?",
        ];
        
        let num_entries = std::cmp::min(self.config.total_requests as usize, default_prompts.len());
        
        default_prompts[..num_entries]
            .iter()
            .map(|prompt| DatasetEntry {
                prompt: Some(prompt.to_string()),
                messages: None,
                expected_tokens: Some(100), // Default expected tokens
                metadata: None,
                _config: None,
            })
            .collect()
    }
    
    /// Validate multimodal content in dataset
    async fn validate_multimodal_content(&self, entries: &[DatasetEntry]) -> Result<()> {
        info!("Validating multimodal content...");
        
        let image_base_path = self.config.image_base_path.as_deref();
        let video_base_path = self.config.video_base_path.as_deref();
        
        let validation_errors = MultimodalUtils::validate_dataset_files(
            entries, 
            image_base_path, 
            video_base_path
        );
        
        if !validation_errors.is_empty() {
            warn!("Found {} validation errors in dataset:", validation_errors.len());
            
            for error in &validation_errors {
                warn!("Entry {}: {}", error.entry_index, error.message);
            }
            
            // For now, we'll warn but not fail
            // In production, you might want to make this configurable
            warn!("Continuing with validation errors. Some requests may fail.");
        } else {
            info!("All multimodal content validated successfully");
        }
        
        Ok(())
    }
    
    /// Log dataset analysis information
    fn log_dataset_analysis(&self, analysis: &DatasetAnalysis) {
        info!("Dataset Analysis:");
        info!("  Total entries: {}", analysis.total_entries);
        info!("  Text-only entries: {}", analysis.text_only_entries);
        info!("  Image entries: {}", analysis.image_entries);
        info!("  Video entries: {}", analysis.video_entries);
        info!("  Mixed entries: {}", analysis.mixed_entries);
        info!("  Total images: {}", analysis.total_images);
        info!("  Total videos: {}", analysis.total_videos);
        info!("  Average expected tokens: {:.1}", analysis.average_expected_tokens());
        
        if analysis.multimodal_entries() > 0 {
            info!("Multimodal dataset detected with {} multimodal entries", analysis.multimodal_entries());
            
            if !self.config.multimodal {
                warn!("Dataset contains multimodal content but --multimodal flag not set");
                warn!("Multimodal content will be processed as text only");
            }
        }
        
        // Estimate processing time
        let time_estimate = MultimodalUtils::estimate_processing_time(analysis);
        info!("Estimated processing time: {:.1} seconds", time_estimate.estimated_total_seconds());
    }
    
    /// Prepare entries for processing (repeat if necessary)
    pub fn prepare_entries(&self, entries: Vec<DatasetEntry>) -> Vec<DatasetEntry> {
        let total_needed = self.config.total_requests as usize;
        let available = entries.len();
        
        if available >= total_needed {
            // Take only what we need
            entries.into_iter().take(total_needed).collect()
        } else {
            // Repeat entries to reach the target
            let mut result = Vec::with_capacity(total_needed);
            let mut cycle = entries.iter().cycle();
            
            for _ in 0..total_needed {
                if let Some(entry) = cycle.next() {
                    result.push(entry.clone());
                }
            }
            
            if available < total_needed {
                info!("Repeating {} entries to reach {} total requests", 
                      available, total_needed);
            }
            
            result
        }
    }
    
    /// Get dataset statistics
    pub fn get_statistics(&self, entries: &[DatasetEntry]) -> DatasetStatistics {
        let analysis = MultimodalUtils::analyze_dataset(entries);
        
        DatasetStatistics {
            total_entries: entries.len(),
            unique_entries: analysis.total_entries,
            text_only_entries: analysis.text_only_entries,
            multimodal_entries: analysis.multimodal_entries(),
            total_images: analysis.total_images,
            total_videos: analysis.total_videos,
            average_expected_tokens: analysis.average_expected_tokens(),
            is_multimodal: analysis.multimodal_entries() > 0,
        }
    }
}

/// Statistics about the loaded dataset
#[derive(Debug, Clone)]
pub struct DatasetStatistics {
    pub total_entries: usize,
    pub unique_entries: usize,
    pub text_only_entries: usize,
    pub multimodal_entries: usize,
    pub total_images: u64,
    pub total_videos: u64,
    pub average_expected_tokens: f64,
    pub is_multimodal: bool,
}

impl DatasetStatistics {
    pub fn repetition_factor(&self) -> f64 {
        if self.unique_entries > 0 {
            self.total_entries as f64 / self.unique_entries as f64
        } else {
            1.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::{tempdir, NamedTempFile};
    use std::io::Write;
    
    fn create_test_config() -> Config {
        Config {
            model: "test-model".to_string(),
            url: "http://localhost:8000".to_string(),
            concurrency: 1,
            total_requests: 5,
            dataset: None,
            max_tokens: 100,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: tempdir().unwrap().into_path(),
            multimodal: false,
            image_base_path: None,
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: None,
            verbose: false,
        }
    }
    
    #[tokio::test]
    async fn test_generate_default_entries() {
        let config = create_test_config();
        let loader = DatasetLoader::new(config);
        
        let entries = loader.generate_default_entries();
        assert_eq!(entries.len(), 5);
        assert!(entries[0].prompt.is_some());
    }
    
    #[tokio::test]
    async fn test_load_from_file() {
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, r#"{{"prompt": "Hello", "expected_tokens": 10}}"#).unwrap();
        writeln!(temp_file, r#"{{"prompt": "World", "expected_tokens": 20}}"#).unwrap();
        temp_file.flush().unwrap();
        
        let mut config = create_test_config();
        config.dataset = Some(temp_file.path().to_path_buf());
        
        let loader = DatasetLoader::new(config);
        let entries = loader.load_dataset().await.unwrap();
        
        assert_eq!(entries.len(), 2);
        assert_eq!(entries[0].prompt, Some("Hello".to_string()));
    }
    
    #[tokio::test]
    async fn test_prepare_entries() {
        let config = create_test_config();
        let loader = DatasetLoader::new(config);
        
        let entries = vec![
            DatasetEntry {
                prompt: Some("Entry 1".to_string()),
                messages: None,
                expected_tokens: None,
                metadata: None,
                _config: None,
            },
            DatasetEntry {
                prompt: Some("Entry 2".to_string()),
                messages: None,
                expected_tokens: None,
                metadata: None,
                _config: None,
            },
        ];
        
        let prepared = loader.prepare_entries(entries);
        assert_eq!(prepared.len(), 5); // Should repeat to reach total_requests
    }
    
    #[test]
    fn test_dataset_statistics() {
        let config = create_test_config();
        let loader = DatasetLoader::new(config);
        
        let entries = vec![
            DatasetEntry {
                prompt: Some("Text entry".to_string()),
                messages: None,
                expected_tokens: Some(10),
                metadata: None,
                _config: None,
            }
        ];
        
        let stats = loader.get_statistics(&entries);
        assert_eq!(stats.total_entries, 1);
        assert_eq!(stats.text_only_entries, 1);
        assert!(!stats.is_multimodal);
    }
}
