use super::{MultimodalError, Result, VideoOptions};
use base64::{engine::general_purpose::STANDARD, Engine as _};
use std::path::Path;
use std::time::Instant;

/// Video processor for handling various video formats and frame extraction
#[derive(Clone)]
pub struct VideoProcessor {
    max_frames: u32,
    frame_quality: u8,
}

impl VideoProcessor {
    pub fn new(max_frames: u32, frame_quality: u8) -> Self {
        Self {
            max_frames,
            frame_quality,
        }
    }
    
    /// Process a video file and return base64 encoded frames with timing info
    pub fn process_video_file(&self, path: &Path, options: &Option<VideoOptions>) -> Result<(Vec<String>, u64, u64)> {
        let start = Instant::now();
        
        if !path.exists() {
            return Err(MultimodalError::FileNotFound(path.to_path_buf()));
        }
        
        // Get file size
        let file_size = std::fs::metadata(path)?.len();
        
        // Extract frames based on available backend
        let frames = self.extract_frames(path, options)?;
        
        let processing_time = start.elapsed().as_millis() as u64;
        
        Ok((frames, processing_time, file_size))
    }
    
    /// Extract frames from video file
    fn extract_frames(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Vec<String>> {
        // Try OpenCV first if available
        #[cfg(feature = "video")]
        {
            if let Ok(frames) = self.extract_frames_opencv(path, options) {
                return Ok(frames);
            }
        }
        
        // Fallback to ffmpeg if available
        self.extract_frames_ffmpeg(path, options)
    }
    
    /// Extract frames using OpenCV (when video feature is enabled)
    #[cfg(feature = "video")]
    fn extract_frames_opencv(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Vec<String>> {
        use opencv::{
            core::{Mat, Vector},
            imgcodecs::{imencode, IMWRITE_JPEG_QUALITY},
            imgproc::{resize, INTER_LINEAR},
            prelude::*,
            videoio::{VideoCapture, CAP_ANY},
        };
        
        let path_str = path.to_str()
            .ok_or_else(|| MultimodalError::ConfigError("Invalid video path".to_string()))?;
        
        let mut cap = VideoCapture::from_file(path_str, CAP_ANY)
            .map_err(|e| MultimodalError::VideoError(format!("Failed to open video: {}", e)))?;
        
        if !cap.is_opened()
            .map_err(|e| MultimodalError::VideoError(format!("Failed to check video status: {}", e)))? {
            return Err(MultimodalError::VideoError("Could not open video file".to_string()));
        }
        
        // Get video properties
        let frame_count = cap.get(opencv::videoio::CAP_PROP_FRAME_COUNT)
            .map_err(|e| MultimodalError::VideoError(format!("Failed to get frame count: {}", e)))? as u32;
        
        let fps = cap.get(opencv::videoio::CAP_PROP_FPS)
            .map_err(|e| MultimodalError::VideoError(format!("Failed to get FPS: {}", e)))?;
        
        // Calculate frame sampling
        let target_fps = options.as_ref().and_then(|o| o.fps).unwrap_or(1.0) as f64;
        let frame_interval = (fps / target_fps).max(1.0) as u32;
        let max_frames = self.max_frames.min(frame_count / frame_interval);
        
        let mut frames = Vec::new();
        let mut frame = Mat::default();
        
        for i in 0..max_frames {
            let frame_index = i * frame_interval;
            
            // Seek to frame
            cap.set(opencv::videoio::CAP_PROP_POS_FRAMES, frame_index as f64)
                .map_err(|e| MultimodalError::VideoError(format!("Failed to seek frame: {}", e)))?;
            
            // Read frame
            cap.read(&mut frame)
                .map_err(|e| MultimodalError::VideoError(format!("Failed to read frame: {}", e)))?;
            
            if frame.empty() {
                break;
            }
            
            // Resize frame if needed
            let resized_frame = self.resize_frame_opencv(&frame)?;
            
            // Encode to JPEG
            let mut buffer = Vector::new();
            let params = Vector::from_slice(&[IMWRITE_JPEG_QUALITY, self.frame_quality as i32]);
            
            imencode(".jpg", &resized_frame, &mut buffer, &params)
                .map_err(|e| MultimodalError::VideoError(format!("Failed to encode frame: {}", e)))?;
            
            // Convert to base64
            let base64_frame = STANDARD.encode(buffer.as_slice());
            frames.push(format!("data:image/jpeg;base64,{}", base64_frame));
        }
        
        Ok(frames)
    }
    
    /// Resize frame using OpenCV
    #[cfg(feature = "video")]
    fn resize_frame_opencv(&self, frame: &opencv::core::Mat) -> Result<opencv::core::Mat> {
        use opencv::{
            core::{Mat, Size},
            imgproc::{resize, INTER_LINEAR},
            prelude::*,
        };
        
        let size = frame.size()
            .map_err(|e| MultimodalError::VideoError(format!("Failed to get frame size: {}", e)))?;
        
        let width = size.width;
        let height = size.height;
        
        // Calculate new size maintaining aspect ratio
        let max_dimension = 1024; // Default max size
        let scale = if width > height {
            max_dimension as f64 / width as f64
        } else {
            max_dimension as f64 / height as f64
        };
        
        if scale >= 1.0 {
            // No need to resize
            return Ok(frame.clone());
        }
        
        let new_width = (width as f64 * scale) as i32;
        let new_height = (height as f64 * scale) as i32;
        
        let mut resized = Mat::default();
        resize(frame, &mut resized, Size::new(new_width, new_height), 0.0, 0.0, INTER_LINEAR)
            .map_err(|e| MultimodalError::VideoError(format!("Failed to resize frame: {}", e)))?;
        
        Ok(resized)
    }
    
    /// Extract frames using ffmpeg command line (fallback)
    fn extract_frames_ffmpeg(&self, path: &Path, options: &Option<VideoOptions>) -> Result<Vec<String>> {
        use std::process::Command;
        use std::fs;
        use std::path::PathBuf;
        
        // Create temporary directory for frames
        let temp_dir = std::env::temp_dir().join(format!("stress_tool_frames_{}", 
            std::process::id()));
        fs::create_dir_all(&temp_dir)?;
        
        // Build ffmpeg command
        let fps = options.as_ref().and_then(|o| o.fps).unwrap_or(1.0);
        let output_pattern = temp_dir.join("frame_%03d.jpg");
        
        let mut cmd = Command::new("ffmpeg");
        cmd.arg("-i").arg(path)
           .arg("-vf").arg(format!("fps={},scale=1024:-1", fps))
           .arg("-q:v").arg(self.frame_quality.to_string())
           .arg("-frames:v").arg(self.max_frames.to_string())
           .arg("-y") // Overwrite output files
           .arg(&output_pattern);
        
        let output = cmd.output()
            .map_err(|e| MultimodalError::VideoError(format!("Failed to run ffmpeg: {}", e)))?;
        
        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(MultimodalError::VideoError(format!("ffmpeg failed: {}", error)));
        }
        
        // Read generated frames
        let mut frames = Vec::new();
        let entries = fs::read_dir(&temp_dir)?;
        
        let mut frame_files: Vec<PathBuf> = entries
            .filter_map(|entry| entry.ok())
            .map(|entry| entry.path())
            .filter(|path| path.extension().map_or(false, |ext| ext == "jpg"))
            .collect();
        
        frame_files.sort();
        
        for frame_path in frame_files {
            let frame_data = fs::read(&frame_path)?;
            let base64_frame = STANDARD.encode(&frame_data);
            frames.push(format!("data:image/jpeg;base64,{}", base64_frame));
        }
        
        // Clean up temporary directory
        let _ = fs::remove_dir_all(&temp_dir);
        
        Ok(frames)
    }
    
    /// Check if video processing is available
    pub fn is_available() -> bool {
        #[cfg(feature = "video")]
        {
            return true;
        }
        
        // Check if ffmpeg is available
        std::process::Command::new("ffmpeg")
            .arg("-version")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }
    
    /// Validate if file is a supported video format
    pub fn is_supported_format(path: &Path) -> bool {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("mp4") | Some("avi") | Some("mov") | Some("mkv") | 
            Some("wmv") | Some("flv") | Some("webm") | Some("mpeg") | Some("mpg") => true,
            _ => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_video_processor_creation() {
        let processor = VideoProcessor::new(10, 80);
        assert_eq!(processor.max_frames, 10);
        assert_eq!(processor.frame_quality, 80);
    }
    
    #[test]
    fn test_supported_formats() {
        assert!(VideoProcessor::is_supported_format(&PathBuf::from("test.mp4")));
        assert!(VideoProcessor::is_supported_format(&PathBuf::from("test.avi")));
        assert!(VideoProcessor::is_supported_format(&PathBuf::from("test.mov")));
        assert!(!VideoProcessor::is_supported_format(&PathBuf::from("test.txt")));
        assert!(!VideoProcessor::is_supported_format(&PathBuf::from("test.jpg")));
    }
    
    #[test]
    fn test_availability_check() {
        // This will return true if either opencv feature is enabled or ffmpeg is available
        let available = VideoProcessor::is_available();
        println!("Video processing available: {}", available);
    }

    #[test]
    fn test_video_file_not_found() {
        let processor = VideoProcessor::new(5, 80);
        let non_existent_path = PathBuf::from("non_existent_video.mp4");
        let result = processor.process_video_file(&non_existent_path, &None);

        assert!(result.is_err());
        match result.unwrap_err() {
            MultimodalError::FileNotFound(path) => {
                assert_eq!(path, non_existent_path);
            }
            _ => panic!("Expected FileNotFound error"),
        }
    }
}
