use super::{DatasetConfig, DatasetEntry, MultimodalError, Result};
use std::fs::File;
use std::io::{BufRead, BufReader};
use std::path::Path;

/// Utility functions for multimodal processing
pub struct MultimodalUtils;

impl MultimodalUtils {
    /// Load dataset from JSONL file
    pub fn load_dataset(path: &Path) -> Result<Vec<DatasetEntry>> {
        let file = File::open(path).map_err(|_| {
            MultimodalError::FileNotFound(path.to_path_buf())
        })?;
        
        let reader = BufReader::new(file);
        let mut entries = Vec::new();
        let mut global_config: Option<DatasetConfig> = None;
        
        for (line_num, line) in reader.lines().enumerate() {
            let line = line.map_err(MultimodalError::IoError)?;
            
            // Skip empty lines
            if line.trim().is_empty() {
                continue;
            }
            
            let entry: DatasetEntry = serde_json::from_str(&line)
                .map_err(|e| MultimodalError::ConfigError(
                    format!("Failed to parse line {}: {}", line_num + 1, e)
                ))?;
            
            // Check if this is a global config entry
            if entry._config.is_some() && entry.prompt.is_none() && entry.messages.is_none() {
                global_config = entry._config;
                continue;
            }
            
            entries.push(entry);
        }
        
        // Apply global config to entries that don't have their own config
        if let Some(_config) = global_config {
            for entry in &mut entries {
                if entry._config.is_none() {
                    // Note: We don't directly modify the entry here as _config is for parsing only
                    // The actual config application happens in the request builder
                }
            }
        }
        
        Ok(entries)
    }
    
    /// Detect if a dataset contains multimodal content
    pub fn is_multimodal_dataset(entries: &[DatasetEntry]) -> bool {
        entries.iter().any(|entry| {
            if let Some(messages) = &entry.messages {
                messages.iter().any(|message| {
                    match &message.content {
                        super::MessageContent::Array(content_array) => {
                            content_array.iter().any(|content| {
                                matches!(content,
                                    super::ContentType::Image { .. } |
                                    super::ContentType::ImageUrl { .. }
                                )
                            })
                        }
                        _ => false,
                    }
                })
            } else {
                false
            }
        })
    }
    
    /// Get statistics about dataset content
    pub fn analyze_dataset(entries: &[DatasetEntry]) -> DatasetAnalysis {
        let mut analysis = DatasetAnalysis::default();
        
        for entry in entries {
            analysis.total_entries += 1;
            
            if entry.prompt.is_some() {
                analysis.text_only_entries += 1;
            } else if let Some(messages) = &entry.messages {
                let mut has_image = false;
                let has_video = false;
                let mut has_text = false;
                
                for message in messages {
                    match &message.content {
                        super::MessageContent::Text(_) => {
                            has_text = true;
                        }
                        super::MessageContent::Array(content_array) => {
                            for content in content_array {
                                match content {
                                    super::ContentType::Text { .. } => has_text = true,
                                    super::ContentType::Image { .. } | 
                                    super::ContentType::ImageUrl { .. } => {
                                        has_image = true;
                                        analysis.total_images += 1;
                                    }
                                    // Video support disabled for now
                                    // super::ContentType::Video { .. } => {
                                    //     has_video = true;
                                    //     analysis.total_videos += 1;
                                    // }
                                }
                            }
                        }
                    }
                }
                
                match (has_text, has_image, has_video) {
                    (true, false, false) => analysis.text_only_entries += 1,
                    (_, true, false) => analysis.image_entries += 1,
                    (_, false, true) => analysis.video_entries += 1,
                    (_, true, true) => analysis.mixed_entries += 1,
                    _ => analysis.other_entries += 1,
                }
            }
            
            if let Some(expected) = entry.expected_tokens {
                analysis.total_expected_tokens += expected as u64;
            }
        }
        
        analysis
    }
    
    /// Validate all files referenced in dataset exist
    pub fn validate_dataset_files(entries: &[DatasetEntry], base_image_path: Option<&Path>, _base_video_path: Option<&Path>) -> Vec<ValidationError> {
        let mut errors = Vec::new();
        
        for (entry_idx, entry) in entries.iter().enumerate() {
            if let Some(messages) = &entry.messages {
                for (msg_idx, message) in messages.iter().enumerate() {
                    if let super::MessageContent::Array(content_array) = &message.content {
                        for (content_idx, content) in content_array.iter().enumerate() {
                            match content {
                                super::ContentType::Image { path, .. } => {
                                    let resolved_path = resolve_path(path, base_image_path);
                                    if !resolved_path.exists() {
                                        errors.push(ValidationError {
                                            entry_index: entry_idx,
                                            message_index: Some(msg_idx),
                                            content_index: Some(content_idx),
                                            error_type: ValidationErrorType::FileNotFound,
                                            path: Some(resolved_path),
                                            message: format!("Image file not found: {}", path),
                                        });
                                    }
                                }
                                // Video support disabled for now
                                // super::ContentType::Video { path, .. } => {
                                //     let resolved_path = resolve_path(path, base_video_path);
                                //     if !resolved_path.exists() {
                                //         errors.push(ValidationError {
                                //             entry_index: entry_idx,
                                //             message_index: Some(msg_idx),
                                //             content_index: Some(content_idx),
                                //             error_type: ValidationErrorType::FileNotFound,
                                //             path: Some(resolved_path),
                                //             message: format!("Video file not found: {}", path),
                                //         });
                                //     }
                                // }
                                _ => {}
                            }
                        }
                    }
                }
            }
        }
        
        errors
    }
    
    /// Convert simple text dataset to multimodal format
    pub fn convert_text_to_multimodal(text_entries: &[String]) -> Vec<DatasetEntry> {
        text_entries.iter().map(|text| {
            DatasetEntry {
                prompt: Some(text.clone()),
                messages: None,
                expected_tokens: None,
                metadata: None,
                _config: None,
            }
        }).collect()
    }
    
    /// Estimate processing time for dataset
    pub fn estimate_processing_time(analysis: &DatasetAnalysis) -> ProcessingTimeEstimate {
        // These are rough estimates based on typical processing times
        let text_time_ms = 10; // Very fast for text
        let image_time_ms = 100; // Image processing
        let video_time_ms = 500; // Video frame extraction
        
        let total_time_ms = 
            analysis.text_only_entries as u64 * text_time_ms +
            analysis.image_entries as u64 * image_time_ms +
            analysis.video_entries as u64 * video_time_ms +
            analysis.mixed_entries as u64 * (image_time_ms + video_time_ms);
        
        ProcessingTimeEstimate {
            estimated_total_ms: total_time_ms,
            estimated_image_processing_ms: analysis.total_images * image_time_ms,
            estimated_video_processing_ms: analysis.total_videos * video_time_ms,
        }
    }
}

fn resolve_path(path: &str, base_path: Option<&Path>) -> std::path::PathBuf {
    let path_buf = std::path::PathBuf::from(path);
    if path_buf.is_absolute() {
        path_buf
    } else if let Some(base) = base_path {
        base.join(path_buf)
    } else {
        path_buf
    }
}

#[derive(Debug, Default)]
pub struct DatasetAnalysis {
    pub total_entries: usize,
    pub text_only_entries: usize,
    pub image_entries: usize,
    pub video_entries: usize,
    pub mixed_entries: usize,
    pub other_entries: usize,
    pub total_images: u64,
    pub total_videos: u64,
    pub total_expected_tokens: u64,
}

impl DatasetAnalysis {
    pub fn multimodal_entries(&self) -> usize {
        self.image_entries + self.video_entries + self.mixed_entries
    }
    
    pub fn average_expected_tokens(&self) -> f64 {
        if self.total_entries > 0 {
            self.total_expected_tokens as f64 / self.total_entries as f64
        } else {
            0.0
        }
    }
}

#[derive(Debug)]
pub struct ValidationError {
    pub entry_index: usize,
    pub message_index: Option<usize>,
    pub content_index: Option<usize>,
    pub error_type: ValidationErrorType,
    pub path: Option<std::path::PathBuf>,
    pub message: String,
}

#[derive(Debug)]
pub enum ValidationErrorType {
    FileNotFound,
    UnsupportedFormat,
    InvalidPath,
}

#[derive(Debug)]
pub struct ProcessingTimeEstimate {
    pub estimated_total_ms: u64,
    pub estimated_image_processing_ms: u64,
    pub estimated_video_processing_ms: u64,
}

impl ProcessingTimeEstimate {
    pub fn estimated_total_seconds(&self) -> f64 {
        self.estimated_total_ms as f64 / 1000.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use std::io::Write;
    
    #[test]
    fn test_dataset_analysis() {
        let entries = vec![
            DatasetEntry {
                prompt: Some("Hello".to_string()),
                messages: None,
                expected_tokens: Some(10),
                metadata: None,
                _config: None,
            },
            DatasetEntry {
                prompt: None,
                messages: Some(vec![
                    super::super::Message {
                        role: "user".to_string(),
                        content: super::super::MessageContent::Array(vec![
                            super::super::ContentType::Text { text: "Describe".to_string() },
                            super::super::ContentType::Image { 
                                path: "test.jpg".to_string(), 
                                description: None, 
                                resize: None 
                            },
                        ]),
                    }
                ]),
                expected_tokens: Some(20),
                metadata: None,
                _config: None,
            },
        ];
        
        let analysis = MultimodalUtils::analyze_dataset(&entries);
        
        assert_eq!(analysis.total_entries, 2);
        assert_eq!(analysis.text_only_entries, 1);
        assert_eq!(analysis.image_entries, 1);
        assert_eq!(analysis.total_images, 1);
        assert_eq!(analysis.total_expected_tokens, 30);
    }
    
    #[test]
    fn test_is_multimodal_dataset() {
        let text_entries = vec![
            DatasetEntry {
                prompt: Some("Hello".to_string()),
                messages: None,
                expected_tokens: None,
                metadata: None,
                _config: None,
            }
        ];
        
        assert!(!MultimodalUtils::is_multimodal_dataset(&text_entries));
        
        let multimodal_entries = vec![
            DatasetEntry {
                prompt: None,
                messages: Some(vec![
                    super::super::Message {
                        role: "user".to_string(),
                        content: super::super::MessageContent::Array(vec![
                            super::super::ContentType::Image { 
                                path: "test.jpg".to_string(), 
                                description: None, 
                                resize: None 
                            },
                        ]),
                    }
                ]),
                expected_tokens: None,
                metadata: None,
                _config: None,
            }
        ];
        
        assert!(MultimodalUtils::is_multimodal_dataset(&multimodal_entries));
    }
    
    #[test]
    fn test_load_dataset() {
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, r#"{{"prompt": "Hello", "expected_tokens": 10}}"#).unwrap();
        writeln!(temp_file, r#"{{"prompt": "World", "expected_tokens": 20}}"#).unwrap();
        temp_file.flush().unwrap();
        
        let entries = MultimodalUtils::load_dataset(temp_file.path()).unwrap();
        assert_eq!(entries.len(), 2);
        assert_eq!(entries[0].prompt, Some("Hello".to_string()));
        assert_eq!(entries[1].expected_tokens, Some(20));
    }
}
