mod config;
mod client;
mod dataset;
mod metrics;
mod multimodal;
mod runner;
mod reporter;
mod tokenizer;

use clap::Parser;
use config::{Args, Config};
use dataset::DatasetLoader;
use multimodal::request::MultimodalRequestBuilder;
use client::ApiClient;
use runner::StressTestRunner;
use reporter::Reporter;
use anyhow::Result;
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments and load config
    let args = Args::parse();
    let config = Config::from_args(args)?;

    // Initialize logging
    init_logging(&config)?;

    // Validate configuration
    config.validate()?;

    info!("Starting stress_tool v{}", env!("CARGO_PKG_VERSION"));
    info!("Target: {} ({})", config.url, config.model);
    info!("Configuration: {} concurrent requests, {} total requests",
          config.concurrency, config.total_requests);

    if config.multimodal {
        info!("Multimodal mode enabled");
    }

    // Load dataset
    let dataset_loader = DatasetLoader::new(config.clone());
    let entries = dataset_loader.load_dataset().await?;
    let prepared_entries = dataset_loader.prepare_entries(entries);

    // Get dataset statistics
    let stats = dataset_loader.get_statistics(&prepared_entries);
    info!("Dataset loaded: {} entries ({} unique)", stats.total_entries, stats.unique_entries);

    if stats.is_multimodal && !config.multimodal {
        warn!("Dataset contains multimodal content but multimodal mode is not enabled");
        warn!("Use --multimodal flag to enable multimodal processing");
    }

    // Create API client
    let api_client = ApiClient::new(config.clone())?;

    // Create multimodal request builder if needed
    let request_builder = if config.multimodal || stats.is_multimodal {
        Some(MultimodalRequestBuilder::new(config.clone()))
    } else {
        None
    };

    info!("Setup complete. Starting stress test...");

    // Create and run stress test
    let runner = StressTestRunner::new(config.clone(), api_client, request_builder);
    let test_stats = runner.run(prepared_entries).await?;

    // Generate reports
    let reporter = Reporter::new(config.clone());
    reporter.generate_report(&test_stats).await?;

    println!("✅ Stress test completed successfully!");
    println!("📊 Results saved to: {}", config.output_dir.display());

    Ok(())
}

fn init_logging(config: &Config) -> Result<()> {
    let filter = if config.verbose {
        "debug"
    } else {
        "info"
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| filter.into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}
