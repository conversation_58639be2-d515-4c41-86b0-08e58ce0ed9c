# stress_tool 产品设计文档

## 1. 产品概述

### 1.1 产品定位
stress_tool 是一个专为大语言模型和多模态模型推理服务设计的性能压测工具，支持文本、图片、视频等多种输入模态，支持流式和非流式场景，提供详细的性能指标分析。

### 1.2 核心价值
- **专业性**：针对 LLM 和多模态模型推理服务的特殊需求（流式返回、token 计算、多媒体处理等）
- **准确性**：本地 tokenizer 预估，精确的延迟测量，多模态输入处理
- **科学性**：系统化的并发递增测试，自动识别最佳并发数和性能瓶颈
- **易用性**：简单的命令行接口，丰富的输出报告和可视化分析
- **可扩展性**：支持多种数据集格式和配置选项，灵活的多模态输入组合

## 2. 功能需求

### 2.1 核心功能
- **并发压测**：支持可配置的并发数
- **并发递增测试**：以2的n次方递增并发数进行系统性性能测试
- **流式支持**：完整支持 SSE 流式响应
- **多模态输入**：支持文本、图片、视频等多种输入模态的组合
- **多指标统计**：延迟、吞吐量、成功率等
- **失败分析**：详细的错误统计和日志
- **性能分析**：自动识别最佳并发数和性能瓶颈点

### 2.2 输入参数
```rust
pub struct Config {
    pub model: String,           // 模型名称
    pub api_base: String,        // API 基础地址
    pub concurrency: u32,        // 并发数
    pub total_requests: u32,     // 总请求数
    pub dataset: Option<PathBuf>, // 数据集文件路径
    pub max_tokens: u32,         // 最大生成 token 数
    pub temperature: f32,        // 采样温度
    pub stream: bool,            // 是否流式
    pub timeout: u64,            // 请求超时时间（秒）
    pub output_dir: PathBuf,     // 输出目录路径
    pub verbose: bool,           // 详细输出模式

    // 多模态相关配置
    pub multimodal: bool,        // 是否启用多模态模式
    pub image_base_path: Option<PathBuf>, // 图片文件基础路径
    pub video_base_path: Option<PathBuf>, // 视频文件基础路径
    pub max_image_size: Option<u32>,      // 最大图片尺寸（像素）
    pub image_quality: Option<u8>,        // 图片质量（1-100）
}
```

### 2.3 输出指标
包括：
- 请求统计（成功/失败数量）
- 时间指标（延迟分布、TTFT、每 token 时间）
- 吞吐量指标（QPS、tokens/sec）
- 多模态指标（图片处理时间、视频处理时间、多媒体数据传输量）
- 系统资源快照

下面给出一份实际运行后生成的 JSON 结果文件示例，并在每个字段后面用注释形式说明：

```json
{
  /* ========== 测试整体信息 ========== */
  "test_id": "20250717143000",               // 测试批次号，自动生成，格式：YYYYMMDDHHMMSS
  "test_config": {                           // 本次压测的所有输入参数
    "model": "gpt-3.5-turbo",                // 被测模型名
    "api_base": "http://127.0.0.1:8000/v1",  // 推理服务地址
    "concurrency": 8,                        // 并发数（多少个协程/线程同时发请求）
    "total_requests": 1000,                  // 计划发出的总请求数
    "dataset": "openqa-500.jsonl",           // 使用的评测数据集
    "max_tokens": 512,                       // 每个请求生成的最大 token 数
    "temperature": 0.1,                      // 采样温度
    "stream": true                           // 是否开启流式返回
  },

  /* ========== 请求统计 ========== */
  "total_requests": 1000,          // 实际发出的请求数
  "succeed_requests": 998,         // HTTP 200 且返回 JSON 正常的请求
  "failed_requests": 2,            // 网络错误/超时/非 200 状态码/解析失败的请求
  "total_prompt_tokens": 245360,   // 所有请求的 prompt token 总数（由本地 tokenizer 预估）
  "total_completion_tokens": 478912,// 所有请求返回的 completion token 总数（由本地 tokenizer 预估）

  /* ========== 时间相关指标 ========== */
  "test_duration_seconds": 63.47,  // 从第 1 个请求发出到最后一个请求响应完成的总耗时
  "first_request_sent": "2025-07-17T14:30:01.123Z",
  "last_request_done":    "2025-07-17T14:31:04.593Z",

  /* ========== 吞吐量指标 ========== */
  "throughput_tokens_per_sec": 7546.8,   // = total_completion_tokens / test_duration_seconds
  "qps": 15.75,                          // = succeed_requests / test_duration_seconds

  /* ========== 延迟指标（毫秒） ========== */
  "average_latency_ms": 496.3,           // 所有成功请求“端到端”延迟的算术平均
                                          // 计算方式：对每个请求记录 (response_done - request_sent)
  "average_time_to_first_token_ms": 38.7,// 对流式返回，指从发送请求到收到首包 chunk 的用时
                                          // 非流式场景下该值与 average_latency_ms 相同
  "average_time_per_output_token_ms": 9.1, // = 平均生成阶段耗时 / 平均输出 token 数
                                           // 生成阶段耗时 = (last_chunk_time - first_chunk_time)
  "p50_latency_ms": 471,                 // 50% 请求延迟 ≤ 471 ms
  "p90_latency_ms": 612,                 // 90% 请求延迟 ≤ 612 ms
  "p95_latency_ms": 689,
  "p99_latency_ms": 812,

  /* ========== 输入/输出长度统计 ========== */
  "avg_prompt_tokens": 245.8,            // = total_prompt_tokens / succeed_requests
  "avg_completion_tokens": 479.9,        // = total_completion_tokens / succeed_requests

  /* ========== 失败统计 ========== */
  "failures": [                          // 仅列出前 10 条，便于排查；全量请查日志
    {
      "request_id": "req-000017",
      "error": "timeout",
      "elapsed_ms": 30002
    },
    {
      "request_id": "req-000234",
      "error": "HTTP 503",
      "elapsed_ms": 45
    }
  ],

  /* ========== 系统资源快照（可选） ========== */
  "system_info": {
    "cpu_count": 16,
    "memory_total_gb": 62.8,
    "gpu_count": 1,
    "gpu_memory_total_mb": 24576,
    "stress_tool_version": "0.4.2"
  },

  /* ========== 原始直方图（可选，用于重绘曲线） ========== */
  "latency_histogram": [                 // 毫秒级桶，每 50 ms 一个桶
    [0, 50, 0],
    [50, 100, 2],
    [100, 150, 5],
    ...
    [800, 850, 1]
  ]
}
```

---

### 指标是如何测得的（测试方法简述）

| 指标 | 采集位置 | 说明 |
|---|---|---|
| total_requests | 压测端计数器 | 每发一条 HTTP 请求就 +1 |
| succeed_requests | 收到 HTTP 200 且 JSON 解析成功 | 否则计入 failed_requests |
| total_prompt_tokens | 本地 tokenizer | 在发送前把 prompt 编码一次即可得到长度 |
| total_completion_tokens | 本地 tokenizer | 把返回的 `choices[0].text` 或 `delta.content` 拼起来再编码 |
| average_latency_ms | 压测端计时 | `time_end - time_start`，`time_start` 为请求发出瞬间，`time_end` 为收到最后一个字节（非流式）或收到最后一个 `finish_reason=stop` 的 chunk（流式） |
| average_time_to_first_token_ms | 流式场景 | 收到第一个非空 chunk 的时间 - `time_start` |
| average_time_per_output_token_ms | 流式场景 | `(last_chunk_time - first_chunk_time) / (completion_tokens - 1)` |
| throughput_tokens_per_sec | 计算得到 | 所有成功请求的 completion_tokens 之和 ÷ 总耗时 |
| qps | 计算得到 | succeed_requests ÷ 总耗时 |
| pXX_latency_ms | 排序后取分位 | 把所有成功请求的延迟放进数组，排序后取对应百分位 |

## 2.4 并发递增测试功能

### 2.4.1 功能概述
并发递增测试（Concurrency Scaling Test）是一种系统性的性能测试方法，通过以2的n次方递增并发数来测试系统在不同负载下的性能表现，帮助用户找到系统的最佳并发数和性能瓶颈点。

### 2.4.2 测试序列设计
**标准测试序列**：1 → 2 → 4 → 8 → 16 → 32 → 64 → 128 → 256 → 512 → 1024

**设计原理**：
- 2的n次方递增能够快速覆盖从低并发到高并发的完整性能区间
- 指数级增长可以高效地定位性能拐点和瓶颈
- 标准化的测试序列便于不同测试结果的对比分析

### 2.4.3 配置参数
```rust
pub struct ConcurrencyScalingConfig {
    // 基础测试配置
    pub base_config: Config,

    // 并发递增测试专用配置
    pub min_concurrency: u32,           // 最小并发数（默认：1）
    pub max_concurrency: u32,           // 最大并发数（默认：1024）
    pub scaling_factor: u32,            // 递增因子（默认：2）
    pub requests_per_level: u32,        // 每个并发级别的请求数
    pub warmup_requests: Option<u32>,   // 预热请求数（可选）
    pub cooldown_duration: Duration,    // 测试间隔时间（默认：5秒）

    // 自适应停止条件
    pub max_failure_rate: f64,          // 最大失败率阈值（默认：50%）
    pub max_avg_latency_ms: u64,        // 最大平均延迟阈值（默认：10000ms）
    pub auto_stop_on_degradation: bool, // 性能严重下降时自动停止

    // 输出配置
    pub output_format: Vec<ReportFormat>, // 支持的报告格式
    pub generate_comparison_chart: bool,  // 生成对比图表
    pub export_raw_data: bool,           // 导出原始数据
}

pub enum ReportFormat {
    Json,
    Html,
    Csv,
    Markdown,
}
```

### 2.4.4 测试执行流程
```
1. 配置验证和初始化
   ├── 验证基础配置参数
   ├── 计算测试序列：[1, 2, 4, 8, 16, ...]
   └── 创建输出目录和日志文件

2. 预热阶段（可选）
   ├── 使用最小并发数执行预热请求
   └── 验证服务可用性

3. 并发递增测试循环
   For each concurrency_level in test_sequence:
   ├── 3.1 测试前准备
   │   ├── 等待冷却时间
   │   ├── 记录测试开始时间
   │   └── 初始化指标收集器
   │
   ├── 3.2 执行压测
   │   ├── 设置并发数为 concurrency_level
   │   ├── 执行 requests_per_level 个请求
   │   └── 实时监控性能指标
   │
   ├── 3.3 结果收集和分析
   │   ├── 收集本轮测试的所有指标
   │   ├── 计算性能指标（QPS、延迟、成功率等）
   │   └── 保存单轮测试报告
   │
   └── 3.4 自适应停止判断
       ├── 检查失败率是否超过阈值
       ├── 检查延迟是否超过阈值
       ├── 检查性能是否严重下降
       └── 决定是否继续下一轮测试

4. 汇总分析和报告生成
   ├── 汇总所有测试轮次的数据
   ├── 生成性能对比分析
   ├── 识别最佳并发数和性能瓶颈
   └── 导出多格式报告
```

### 2.4.5 核心算法设计

**1. 测试序列生成算法**
```rust
fn generate_concurrency_sequence(min: u32, max: u32, factor: u32) -> Vec<u32> {
    let mut sequence = Vec::new();
    let mut current = min;

    while current <= max {
        sequence.push(current);
        current = current.saturating_mul(factor);
        if current == current.saturating_mul(factor) {
            break; // 防止溢出导致的无限循环
        }
    }

    sequence
}
```

**2. 自适应停止算法**
```rust
fn should_stop_testing(
    current_result: &TestResult,
    previous_results: &[TestResult],
    config: &ConcurrencyScalingConfig
) -> (bool, StopReason) {
    // 失败率检查
    if current_result.failure_rate > config.max_failure_rate {
        return (true, StopReason::HighFailureRate);
    }

    // 延迟检查
    if current_result.avg_latency_ms > config.max_avg_latency_ms {
        return (true, StopReason::HighLatency);
    }

    // 性能下降检查
    if config.auto_stop_on_degradation && previous_results.len() >= 2 {
        let performance_degradation = calculate_performance_degradation(
            current_result,
            previous_results
        );
        if performance_degradation > 0.5 { // 性能下降超过50%
            return (true, StopReason::PerformanceDegradation);
        }
    }

    (false, StopReason::None)
}
```

**3. 性能分析算法**
```rust
fn analyze_performance_characteristics(results: &[TestResult]) -> PerformanceAnalysis {
    PerformanceAnalysis {
        optimal_concurrency: find_optimal_concurrency(results),
        bottleneck_point: find_bottleneck_point(results),
        scalability_factor: calculate_scalability_factor(results),
        performance_curve: generate_performance_curve(results),
        recommendations: generate_recommendations(results),
    }
}
```

### 2.4.6 输出报告设计

**1. 汇总报告结构**
```json
{
  "test_metadata": {
    "test_id": "scaling_test_1752711602",
    "timestamp": "2025-07-17T00:20:02Z",
    "test_duration_seconds": 1800,
    "test_sequence": [1, 2, 4, 8, 16, 32, 64, 128],
    "stop_reason": "HighLatency",
    "base_config": { /* 基础配置信息 */ }
  },

  "performance_summary": {
    "optimal_concurrency": 32,
    "max_qps": 245.6,
    "max_tokens_per_second": 1234.5,
    "bottleneck_concurrency": 64,
    "scalability_rating": "Good"
  },

  "test_results": [
    {
      "concurrency": 1,
      "summary": { /* 标准测试摘要 */ },
      "latency_metrics": { /* 延迟指标 */ },
      "token_metrics": { /* Token指标 */ },
      "ttft_metrics": { /* TTFT指标 */ },
      "multimodal_metrics": { /* 多模态指标 */ },
      "errors": { /* 错误统计 */ }
    },
    // ... 其他并发级别的结果
  ],

  "performance_analysis": {
    "scalability_curve": {
      "concurrency_levels": [1, 2, 4, 8, 16, 32, 64],
      "qps_values": [12.3, 24.1, 47.8, 89.2, 156.7, 245.6, 198.4],
      "latency_p95_values": [120, 125, 135, 180, 280, 450, 1200],
      "success_rates": [100, 100, 99.8, 99.5, 98.2, 95.1, 87.3]
    },

    "performance_insights": {
      "linear_scaling_range": [1, 16],
      "optimal_range": [16, 32],
      "degradation_start": 64,
      "bottleneck_indicators": ["high_latency", "connection_errors"]
    },

    "recommendations": [
      "建议生产环境并发数设置为 16-32",
      "在并发数超过 64 时系统出现明显性能下降",
      "主要瓶颈为网络连接数限制，建议优化连接池配置"
    ]
  }
}
```

**2. HTML可视化报告特性**
- **性能曲线图**：QPS、延迟、成功率随并发数变化的趋势图
- **对比表格**：各并发级别的详细指标对比
- **性能分析**：自动识别最佳并发数和瓶颈点
- **交互式图表**：支持缩放、筛选和数据点详情查看
- **导出功能**：支持导出图表和数据

### 2.4.7 命令行接口设计
```bash
# 基础并发递增测试
stress_tool scaling \
  --api-base "https://api.example.com/v1/chat/completions" \
  --model "gpt-4" \
  --dataset examples/datasets/simple_text.jsonl \
  --requests-per-level 100 \
  --max-concurrency 256

# 自定义测试序列
stress_tool scaling \
  --api-base "https://api.example.com/v1/chat/completions" \
  --model "gpt-4" \
  --dataset examples/datasets/simple_text.jsonl \
  --min-concurrency 4 \
  --max-concurrency 128 \
  --scaling-factor 2 \
  --requests-per-level 50 \
  --cooldown-duration 10s

# 带自适应停止条件的测试
stress_tool scaling \
  --api-base "https://api.example.com/v1/chat/completions" \
  --model "gpt-4" \
  --dataset examples/datasets/simple_text.jsonl \
  --max-failure-rate 0.3 \
  --max-avg-latency 5000 \
  --auto-stop-on-degradation \
  --output-format html,json,csv

# 多模态并发递增测试
stress_tool scaling \
  --api-base "https://api.example.com/v1/chat/completions" \
  --model "gpt-4-vision" \
  --dataset examples/datasets/multimodal_images.jsonl \
  --multimodal \
  --image-base-path ./test_images \
  --requests-per-level 30 \
  --max-concurrency 64
```

### 2.4.8 可复用性设计

**1. 模块化架构**
```rust
// 核心测试引擎 - 可复用于不同类型的递增测试
pub struct ScalingTestEngine<T: TestExecutor> {
    executor: T,
    config: ScalingConfig,
    metrics_collector: MetricsCollector,
    reporter: Reporter,
}

// 抽象测试执行器 - 支持不同的测试类型
pub trait TestExecutor {
    type Config;
    type Result;

    async fn execute_test(&self, config: &Self::Config) -> Result<Self::Result>;
    fn validate_config(&self, config: &Self::Config) -> Result<()>;
}

// 并发测试执行器的具体实现
pub struct ConcurrencyTestExecutor {
    client: ApiClient,
    dataset: Dataset,
}

impl TestExecutor for ConcurrencyTestExecutor {
    type Config = ConcurrencyTestConfig;
    type Result = TestStats;

    async fn execute_test(&self, config: &Self::Config) -> Result<Self::Result> {
        // 执行单轮并发测试
    }
}
```

**2. 扩展性支持**
- **自定义测试序列**：支持用户定义任意递增序列（如斐波那契数列、自定义步长等）
- **多维度递增**：支持同时测试并发数和其他参数（如token数、温度等）的组合
- **插件化分析**：支持自定义性能分析算法和报告格式
- **集成接口**：提供API接口供其他工具集成使用

**3. 标准化接口**
```rust
// 标准化的测试结果接口
pub trait TestResult {
    fn get_metrics(&self) -> &TestMetrics;
    fn get_errors(&self) -> &ErrorStats;
    fn is_successful(&self) -> bool;
    fn get_duration(&self) -> Duration;
}

// 标准化的报告生成接口
pub trait ReportGenerator {
    fn generate_summary(&self, results: &[Box<dyn TestResult>]) -> Result<String>;
    fn generate_detailed_report(&self, results: &[Box<dyn TestResult>]) -> Result<String>;
    fn export_raw_data(&self, results: &[Box<dyn TestResult>]) -> Result<Vec<u8>>;
}
```

## 3. 技术架构

### 3.1 技术栈
- **语言**：Rust（高性能、内存安全）
- **异步运行时**：tokio（并发处理）
- **HTTP 客户端**：reqwest（支持 SSE）
- **JSON 处理**：serde_json
- **Tokenizer**：tiktoken-rs 或 tokenizers
- **图像处理**：image crate（图片加载、格式转换、尺寸调整）
- **视频处理**：ffmpeg-next 或 opencv（视频帧提取、格式转换）
- **Base64 编码**：base64 crate（多媒体数据编码）

### 3.2 核心模块
```
src/
├── main.rs              // 入口点和 CLI 解析
├── config.rs            // 配置管理
├── client.rs            // HTTP 客户端封装
├── tokenizer.rs         // Token 计算
├── metrics.rs           // 指标收集和统计
├── dataset.rs           // 数据集加载
├── runner.rs            // 压测执行器
├── reporter.rs          // 结果输出
├── scaling/             // 并发递增测试模块
│   ├── mod.rs           // 模块入口
│   ├── engine.rs        // 测试引擎
│   ├── analyzer.rs      // 性能分析器
│   └── reporter.rs      // 递增测试报告生成
└── multimodal/          // 多模态处理模块
    ├── mod.rs           // 模块入口
    ├── image.rs         // 图片处理（加载、转换、编码）
    ├── video.rs         // 视频处理（帧提取、编码）
    ├── request.rs       // 多模态请求构建
    └── utils.rs         // 多模态工具函数
```

### 3.3 数据流

**标准压测流程**：
1. 加载配置和数据集
2. 初始化 tokenizer 和 HTTP 客户端
3. 预处理多模态数据（图片/视频加载、格式转换、Base64编码）
4. 启动并发任务执行请求
5. 实时收集指标数据（包括多模态处理时间）
6. 生成统计报告和日志

**并发递增测试流程**：
1. 加载基础配置和数据集
2. 生成测试序列（1, 2, 4, 8, 16, ...）
3. 对每个并发级别：
   - 执行标准压测流程
   - 收集性能指标
   - 判断是否满足停止条件
4. 汇总所有测试结果
5. 执行性能分析和瓶颈识别
6. 生成综合报告和可视化图表

## 4. 多模态功能设计

### 4.1 支持的输入模态
- **文本**：纯文本输入，与现有功能兼容
- **图片**：支持 JPEG、PNG、WebP 等常见格式
- **视频**：支持 MP4、AVI、MOV 等格式（提取关键帧）
- **混合模态**：文本+图片、文本+视频、文本+图片+视频等组合

### 4.2 数据集格式
#### 4.2.1 纯文本数据集（兼容现有格式）
```jsonl
{"prompt": "What is the capital of France?", "expected_tokens": 10}
{"prompt": "Explain quantum computing", "expected_tokens": 200}
```

#### 4.2.2 多模态数据集格式
```jsonl
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "Describe this image"},
      {"type": "image", "path": "images/sample1.jpg"}
    ]}
  ],
  "expected_tokens": 50
}
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "What happens in this video?"},
      {"type": "video", "path": "videos/sample1.mp4", "fps": 1.0}
    ]}
  ],
  "expected_tokens": 100
}
```

### 4.3 API 请求格式
支持 OpenAI Vision API 兼容格式：
```json
{
  "model": "gpt-4-vision-preview",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What's in this image?"},
        {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
      ]
    }
  ],
  "max_tokens": 100
}
```

### 4.4 性能优化策略
- **图片预处理**：批量加载和缓存，避免重复处理
- **尺寸优化**：根据模型要求调整图片尺寸，减少传输量
- **并发处理**：多模态数据处理与网络请求并行
- **内存管理**：及时释放大型多媒体数据，避免内存泄漏

## 5. 用户体验设计

### 5.1 命令行接口
```bash
stress_tool [OPTIONS] --api-base <URL>

OPTIONS:
    -m, --model <MODEL>              模型名称 [default: gpt-3.5-turbo]
    -u, --api-base <URL>             API 端点 URL
    -c, --concurrency <NUM>          并发数 [default: 1]
    -n, --number <NUM>               总请求数 [default: 100]
    -d, --dataset <FILE>             数据集文件路径
    -t, --max-tokens <NUM>           最大生成 tokens [default: 512]
        --temperature <FLOAT>        采样温度 [default: 0.1]
        --stream                     启用流式模式
        --timeout <SECONDS>          请求超时时间 [default: 30]
    -o, --output <DIR>               输出目录 [default: benchmark]

    # 多模态相关选项
        --multimodal                 启用多模态模式
        --image-base-path <PATH>     图片文件基础路径
        --video-base-path <PATH>     视频文件基础路径
        --max-image-size <PIXELS>    最大图片尺寸 [default: 1024]
        --image-quality <QUALITY>    图片质量 1-100 [default: 85]

    -h, --help                       显示帮助信息
```

### 5.2 基本使用示例
```bash
# 基本文本压测
stress_tool --model gpt-3.5-turbo \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --concurrency 8 \
  --number 1000

# 流式压测
stress_tool --model gpt-3.5-turbo \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --stream \
  --concurrency 4 \
  --number 500
```

### 5.3 并发递增测试示例
```bash
# 基础并发递增测试
stress_tool scaling \
  --model gpt-3.5-turbo \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --requests-per-level 100 \
  --max-concurrency 256

# 自定义测试参数
stress_tool scaling \
  --model gpt-4 \
  --api-base https://api.openai.com/v1/chat/completions \
  --dataset text_dataset.jsonl \
  --min-concurrency 2 \
  --max-concurrency 128 \
  --requests-per-level 50 \
  --cooldown-duration 10s \
  --max-failure-rate 0.3 \
  --output-format html,json,csv
```

### 5.4 输出格式
- **实时进度**：显示当前进度和实时 QPS，包括多模态数据处理进度
- **结果文件**：JSON 格式的详细报告，包含多模态相关指标
- **日志文件**：包含所有请求的详细日志，记录多模态数据处理过程
- **并发递增测试报告**：包含性能曲线图、最佳并发数推荐和瓶颈分析

### 5.5 多模态使用示例
```bash
# 图片压测
stress_tool --multimodal \
  --model gpt-4-vision-preview \
  --api-base https://api.example.com/v1/chat/completions \
  --dataset multimodal_images.jsonl \
  --image-base-path ./test_images \
  --concurrency 4 \
  --number 100

# 视频压测
stress_tool --multimodal \
  --model gpt-4-vision-preview \
  --api-base https://api.example.com/v1/chat/completions \
  --dataset multimodal_videos.jsonl \
  --video-base-path ./test_videos \
  --concurrency 2 \
  --number 50
```

## 6. 实现进度

### 6.1 当前版本状态（v0.1.0）
**实际实现进度：85% 完成度，核心功能完备**

基于 2025-07-16 的压测验证结果，使用 `https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions` 进行的全面测试显示：

#### ✅ 已完成并验证的功能

**核心压测功能（v0.1.0 目标）**
- [x] 基础并发压测 ✓ 验证通过（并发2，成功率100%）
- [x] 延迟和吞吐量统计 ✓ 验证通过（平均延迟3606ms，QPS 0.48）
- [x] 成功率统计 ✓ 验证通过（所有测试100%成功率）
- [x] 基础错误处理 ✓ 验证通过（错误统计和日志完备）

**流式支持（v0.2.0 目标）**
- [x] SSE 流式响应解析 ✓ 验证通过（流式测试成功）
- [x] 流式延迟测量 ✓ 验证通过（平均延迟3222ms）
- [x] 流式 token 统计 ⚠️ 部分问题（prompt tokens显示为0）
- [x] 流式错误处理 ✓ 验证通过

**多模态支持（v0.3.0 目标）**
- [x] 图片输入处理 ✓ 验证通过（成功处理3张图片）
- [x] 多模态请求构建 ✓ 验证通过（多模态测试成功）
- [x] 图片预处理和编码 ✓ 验证通过（平均54ms处理时间）
- [x] 多模态错误处理 ✓ 验证通过
- [x] 视频输入处理 ⚠️ 代码存在但未验证

**高级功能（v0.4.0 目标）**
- [x] 详细的性能指标收集 ✓ 验证通过（完整指标输出）
- [x] JSON/CSV/TXT 多格式报告 ✓ 验证通过（三种格式正常生成）
- [x] 本地 tokenizer 集成 ✓ 基础实现完成
- [x] 灵活的数据集格式支持 ✓ 验证通过（JSONL格式支持）
- [x] 完整的配置系统 ✓ 验证通过（CLI参数完备）

#### � 需要修复的问题

**Token 统计问题（优先级：P0）**
- [ ] 流式场景下 `total_prompt_tokens` 显示为0
- [ ] TTFT（Time to First Token）指标缺失或不准确
- [ ] Token计算在某些场景下不够精确

**命令行接口不一致（优先级：P0）**
- [x] 设计文档和实际实现已统一使用 `--api-base` 参数
- [ ] README文档中的示例需要更新保持一致

**代码质量问题（优先级：P1）**
- [ ] 编译时存在40个警告（未使用的导入、变量、方法）
- [ ] 部分模块的错误处理可以更加细化

**功能完善（优先级：P1）**
- [ ] 视频处理功能未充分测试验证
- [ ] HTML 可视化报告功能缺失
- [ ] 实时监控界面未实现

#### ❌ 待实现功能

**报告系统增强**
- [ ] HTML 可视化报告（包含延迟分布图表）
- [ ] 实时监控界面
- [ ] 历史测试对比功能

**用户体验优化**
- [ ] 交互式配置向导
- [ ] 更友好的错误提示和帮助信息
- [ ] 进度显示优化

**高级分析功能**
- [ ] 性能瓶颈自动分析
- [ ] 自动性能调优建议
- [ ] 分布式压测支持

### 6.2 压测验证结果总结

#### 测试环境
- **API端点**: `https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions`
- **模型**: `Qwen/Qwen2.5-VL-32B-Instruct`
- **测试时间**: 2025-07-16

#### 测试结果
1. **文本压测**: 5请求/并发2 → 100%成功率，3606ms平均延迟
2. **多模态压测**: 3请求/并发1 → 100%成功率，4750ms平均延迟，3张图片处理
3. **流式压测**: 3请求/并发1 → 100%成功率，3222ms平均延迟

#### 架构验证
- ✅ 模块化设计完全符合设计文档
- ✅ 异步并发处理正常工作
- ✅ 错误处理框架完备
- ✅ 输出格式与设计文档高度一致

### 6.3 技术债务和修复计划

#### 立即修复（本周内）
1. **修复token统计问题** - 确保prompt tokens正确计算
2. **实现TTFT指标** - 添加首token时间测量
3. **统一命令行参数** - 确保所有文档使用--api-base参数
4. **清理代码警告** - 移除未使用的导入和变量

#### 短期完善（2周内）
1. **完善视频处理功能** - 测试和验证video.rs模块
2. **添加HTML可视化报告** - 实现图表和可视化
3. **提升错误处理** - 更细化的错误分类和重试机制
4. **增加测试覆盖率** - 补充单元测试和集成测试

#### 中期规划（1个月内）
1. **实时监控界面** - Web界面实时显示测试进度
2. **性能分析功能** - 自动瓶颈识别和优化建议
3. **历史对比功能** - 测试结果历史趋势分析
4. **文档完善** - 用户指南和最佳实践

### 6.4 版本发布计划

#### v0.1.1（Bug修复版本）- 1周内
- 修复token统计和TTFT指标问题
- 统一命令行参数命名
- 清理所有编译警告
- 完善错误处理

#### v0.1.2（功能增强版本）- 2周内
- 完善视频处理功能验证
- 添加HTML可视化报告
- 提升测试覆盖率
- 优化性能和稳定性

#### v0.2.0（用户体验版本）- 1个月内
- 实时监控界面
- 交互式配置向导
- 性能分析和建议功能
- 完整的用户文档

### 6.5 质量保证策略

#### 测试验证
- ✅ 已通过真实API端点压测验证
- ✅ 多模态功能实际验证通过
- ✅ 流式处理功能验证通过
- 🔄 需要增加更多边界情况测试

#### 代码质量
- 目标：零警告编译
- 目标：80%+测试覆盖率
- 持续集成检查
- 代码审查流程

#### 文档同步
- 设计文档与实现保持一致
- API文档自动生成
- 示例代码定期验证
- 故障排除指南完善
- 测试覆盖率提升
- 质量保证完善

**v0.2.0（并发递增测试版本）** - 预计 3 周
- ✅ 可视化报告功能（已完成）
- 🔄 并发递增测试功能（开发中）
  - 核心测试引擎实现
  - 自适应停止算法
  - 性能分析和建议生成
  - 汇总报告和可视化
- 用户体验优化

**v0.3.0（高级分析版本）** - 预计 2 周
- 多维度递增测试（并发数 + token数 + 温度等参数组合）
- 自定义测试序列支持（斐波那契、自定义步长等）
- 性能回归检测和对比分析
- 集成API接口，支持CI/CD集成
- 插件化分析算法支持

## 7. 质量保证

### 7.1 测试策略
- 单元测试：各模块功能测试，包括多模态处理模块和并发递增测试引擎
- 集成测试：端到端压测流程，覆盖多模态场景和并发递增测试场景
- 性能测试：工具本身的性能验证，多模态数据处理性能，递增测试算法性能
- 兼容性测试：不同格式的图片、视频文件处理
- 算法测试：性能分析算法准确性验证，自适应停止条件测试

### 7.2 错误处理
- 网络错误重试机制
- 优雅的超时处理
- 详细的错误日志记录
- 多模态数据处理错误处理（文件不存在、格式不支持等）

## 8. 部署和分发

### 8.1 构建方式
- 支持多平台编译（Linux、macOS、Windows）
- 提供预编译二进制文件
- 支持 Docker 容器化部署
- 多模态依赖库的静态链接或动态链接配置

### 8.2 配置管理
- 命令行参数优先级最高
- 支持配置文件（YAML/JSON）
- 环境变量支持
- 多模态相关配置的验证和默认值设置
