
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report - 1752711602</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-bg: #f8f9fa;
            --border-color: #e0e0e0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header .subtitle {
            color: #666;
            font-size: 1.1em;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-card.success { border-left-color: var(--success-color); }
        .metric-card.warning { border-left-color: var(--warning-color); }
        .metric-card.danger { border-left-color: var(--danger-color); }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .metric-label {
            color: #666;
            font-size: 1em;
            font-weight: 500;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 25px;
            color: #333;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
        }

        .config-table, .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .config-table th,
        .config-table td,
        .details-table th,
        .details-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .config-table th,
        .details-table th {
            background: var(--light-bg);
            font-weight: 600;
            color: #555;
        }

        .config-table td {
            word-break: break-all;
        }

        .config-table tr:hover,
        .details-table tr:hover {
            background: var(--light-bg);
        }

        .error-section {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .error-title {
            color: var(--danger-color);
            font-weight: bold;
            margin-bottom: 15px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 1em;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Stress Test Report</h1>
            <div class="subtitle">Test ID: <span id="test-id"></span> | Generated: <span id="timestamp"></span></div>
        </div>

        <div class="metrics-grid" id="metrics-grid">
            <!-- Metrics will be populated by JavaScript -->
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 Response Time Distribution</div>
            <canvas id="histogramChart" width="400" height="200"></canvas>
        </div>

        <div class="section">
            <h3>📈 Detailed Metrics</h3>
            <div class="tabs">
                <button class="tab active" onclick="showTab('latency')">Latency</button>
                <button class="tab" onclick="showTab('tokens')">Tokens</button>
                <button class="tab" onclick="showTab('ttft')">TTFT</button>
                <button class="tab" onclick="showTab('multimodal')">Multimodal</button>
            </div>

            <div id="latency-content" class="tab-content active">
                <table class="details-table" id="latency-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="tokens-content" class="tab-content">
                <table class="details-table" id="tokens-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="ttft-content" class="tab-content">
                <table class="details-table" id="ttft-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>

            <div id="multimodal-content" class="tab-content">
                <table class="details-table" id="multimodal-table">
                    <!-- Populated by JavaScript -->
                </table>
            </div>
        </div>

        <div class="section">
            <h3>⚙️ Test Configuration</h3>
            <table class="config-table" id="config-table">
                <!-- Populated by JavaScript -->
            </table>
        </div>

        <div class="section" id="errors-section" style="display: none;">
            <h3>❌ Errors</h3>
            <div id="errors-content">
                <!-- Populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Embedded test data
        const testData = {"errors":{"error_counts":{"Failed to send request":3},"error_examples":[{"error":"Failed to send request","response_time_ms":0,"status_code":0},{"error":"Failed to send request","response_time_ms":0,"status_code":0},{"error":"Failed to send request","response_time_ms":0,"status_code":0}]},"latency_metrics":{"average_ms":0,"histogram":{"labels":["0-1","1-2","2-3","3-4","4-5","5-6","6-7","7-8","8-9","9-10","10-11","11-12","12-13","13-14","14-15","15-16","16-17","17-18","18-19","19-20"],"values":[3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},"max_ms":0,"min_ms":0,"percentiles_ms":{"p50":0,"p90":0,"p95":0,"p99":0}},"multimodal_metrics":{"average_image_processing_time_ms":0.0,"average_video_processing_time_ms":0.0,"image_processing_errors":0,"images_processed":0,"total_image_size_bytes":0,"total_video_size_bytes":0,"video_processing_errors":0,"videos_processed":0},"request_details":[{"completion_tokens":null,"error_message":"Failed to send request","prompt_tokens":null,"response_time_ms":0,"status_code":0,"success":false,"time_to_first_token_ms":null,"total_tokens":null},{"completion_tokens":null,"error_message":"Failed to send request","prompt_tokens":null,"response_time_ms":0,"status_code":0,"success":false,"time_to_first_token_ms":null,"total_tokens":null},{"completion_tokens":null,"error_message":"Failed to send request","prompt_tokens":null,"response_time_ms":0,"status_code":0,"success":false,"time_to_first_token_ms":null,"total_tokens":null}],"summary":{"failed_requests":3,"qps":0.04999300156969766,"success_rate":0.0,"successful_requests":0,"test_duration_seconds":60.008399292,"tokens_per_second":0.0,"total_requests":3},"test_config":{"concurrency":2,"dataset":"examples/datasets/simple_text.jsonl","max_tokens":80,"model":"Qwen/Qwen2.5-VL-32B-Instruct","multimodal":false,"stream":false,"temperature":0.10000000149011612,"timeout":30,"total_requests":3,"url":"https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"},"test_id":"1752711602","timestamp":"2025-07-17T00:20:02.355059+00:00","token_metrics":{"average_tokens_per_request":0.0,"total_completion_tokens":0,"total_prompt_tokens":0,"total_tokens":0},"ttft_metrics":{"average_ms":0.0,"count":0,"max_ms":0,"min_ms":0}};

        // Initialize the report
        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
        });

        function initializeReport() {
            populateHeader();
            populateMetrics();
            populateChart();
            populateDetailedMetrics();
            populateConfiguration();
            populateErrors();
        }

        function populateHeader() {
            document.getElementById('test-id').textContent = testData.test_id || 'Unknown';
            document.getElementById('timestamp').textContent = new Date(testData.timestamp).toLocaleString() || 'Unknown';
        }

        function populateMetrics() {
            const summary = testData.summary || {};
            const latency = testData.latency_metrics || {};
            const tokens = testData.token_metrics || {};

            const metricsGrid = document.getElementById('metrics-grid');
            metricsGrid.innerHTML = `
                <div class="metric-card success">
                    <div class="metric-value">${(summary.success_rate * 100).toFixed(1)}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${latency.average_ms || 0}</div>
                    <div class="metric-label">Avg Response Time (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${summary.qps?.toFixed(2) || '0.00'}</div>
                    <div class="metric-label">Requests/sec</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${summary.tokens_per_second?.toFixed(2) || '0.00'}</div>
                    <div class="metric-label">Tokens/sec</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${summary.total_requests || 0}</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${summary.test_duration_seconds?.toFixed(1) || '0.0'}s</div>
                    <div class="metric-label">Duration</div>
                </div>
            `;
        }

        function populateChart() {
            const latency = testData.latency_metrics || {};
            const histogram = latency.histogram || { labels: [], values: [] };

            const ctx = document.getElementById('histogramChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: histogram.labels,
                    datasets: [{
                        label: 'Request Count',
                        data: histogram.values,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                        title: {
                            display: true,
                            text: 'Response Time Distribution (ms)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: 'Number of Requests' }
                        },
                        x: {
                            title: { display: true, text: 'Response Time (ms)' }
                        }
                    }
                }
            });
        }

        function populateDetailedMetrics() {
            // Latency metrics
            const latency = testData.latency_metrics || {};
            const percentiles = latency.percentiles_ms || {};
            document.getElementById('latency-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Average</td><td>${latency.average_ms || 0} ms</td></tr>
                <tr><td>Minimum</td><td>${latency.min_ms || 0} ms</td></tr>
                <tr><td>Maximum</td><td>${latency.max_ms || 0} ms</td></tr>
                <tr><td>P50 (Median)</td><td>${percentiles.p50 || 0} ms</td></tr>
                <tr><td>P90</td><td>${percentiles.p90 || 0} ms</td></tr>
                <tr><td>P95</td><td>${percentiles.p95 || 0} ms</td></tr>
                <tr><td>P99</td><td>${percentiles.p99 || 0} ms</td></tr>
            `;

            // Token metrics
            const tokens = testData.token_metrics || {};
            document.getElementById('tokens-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Total Tokens</td><td>${tokens.total_tokens || 0}</td></tr>
                <tr><td>Prompt Tokens</td><td>${tokens.total_prompt_tokens || 0}</td></tr>
                <tr><td>Completion Tokens</td><td>${tokens.total_completion_tokens || 0}</td></tr>
                <tr><td>Avg Tokens/Request</td><td>${tokens.average_tokens_per_request?.toFixed(1) || '0.0'}</td></tr>
            `;

            // TTFT metrics
            const ttft = testData.ttft_metrics || {};
            document.getElementById('ttft-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Average TTFT</td><td>${ttft.average_ms?.toFixed(0) || 0} ms</td></tr>
                <tr><td>Min TTFT</td><td>${ttft.min_ms || 0} ms</td></tr>
                <tr><td>Max TTFT</td><td>${ttft.max_ms || 0} ms</td></tr>
                <tr><td>TTFT Count</td><td>${ttft.count || 0}</td></tr>
            `;

            // Multimodal metrics
            const multimodal = testData.multimodal_metrics || {};
            document.getElementById('multimodal-table').innerHTML = `
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Images Processed</td><td>${multimodal.images_processed || 0}</td></tr>
                <tr><td>Videos Processed</td><td>${multimodal.videos_processed || 0}</td></tr>
                <tr><td>Avg Image Processing Time</td><td>${multimodal.average_image_processing_time_ms?.toFixed(2) || '0.00'} ms</td></tr>
                <tr><td>Avg Video Processing Time</td><td>${multimodal.average_video_processing_time_ms?.toFixed(2) || '0.00'} ms</td></tr>
                <tr><td>Total Image Size</td><td>${formatBytes(multimodal.total_image_size_bytes || 0)}</td></tr>
                <tr><td>Total Video Size</td><td>${formatBytes(multimodal.total_video_size_bytes || 0)}</td></tr>
                <tr><td>Image Processing Errors</td><td>${multimodal.image_processing_errors || 0}</td></tr>
                <tr><td>Video Processing Errors</td><td>${multimodal.video_processing_errors || 0}</td></tr>
            `;
        }

        function populateConfiguration() {
            const config = testData.test_config || {};
            document.getElementById('config-table').innerHTML = `
                <tr><th>Setting</th><th>Value</th></tr>
                <tr><td>Target URL</td><td>${config.url || 'N/A'}</td></tr>
                <tr><td>Model</td><td>${config.model || 'N/A'}</td></tr>
                <tr><td>Concurrency</td><td>${config.concurrency || 0}</td></tr>
                <tr><td>Total Requests</td><td>${config.total_requests || 0}</td></tr>
                <tr><td>Max Tokens</td><td>${config.max_tokens || 0}</td></tr>
                <tr><td>Temperature</td><td>${config.temperature || 0}</td></tr>
                <tr><td>Streaming</td><td>${config.stream ? 'Yes' : 'No'}</td></tr>
                <tr><td>Timeout</td><td>${config.timeout || 0}s</td></tr>
                <tr><td>Multimodal</td><td>${config.multimodal ? 'Yes' : 'No'}</td></tr>
                <tr><td>Dataset</td><td>${config.dataset || 'N/A'}</td></tr>
            `;
        }

        function populateErrors() {
            const errors = testData.errors || {};
            const errorCounts = errors.error_counts || {};

            if (Object.keys(errorCounts).length > 0) {
                document.getElementById('errors-section').style.display = 'block';
                let errorHtml = '<div class="error-section">';
                errorHtml += '<div class="error-title">Error Summary</div>';
                errorHtml += '<table class="details-table"><tr><th>Error Type</th><th>Count</th></tr>';

                for (const [error, count] of Object.entries(errorCounts)) {
                    errorHtml += `<tr><td>${error}</td><td>${count}</td></tr>`;
                }

                errorHtml += '</table></div>';
                document.getElementById('errors-content').innerHTML = errorHtml;
            }
        }

        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
