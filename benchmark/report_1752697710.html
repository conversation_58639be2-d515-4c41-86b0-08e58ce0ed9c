
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report - 1752697710</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .header .subtitle {
            color: #666;
            margin-top: 10px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .metric-card.success {
            border-left-color: #28a745;
        }
        .metric-card.warning {
            border-left-color: #ffc107;
        }
        .metric-card.danger {
            border-left-color: #dc3545;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .chart-container {
            margin: 40px 0;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .config-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .config-section h3 {
            margin-top: 0;
            color: #333;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .config-label {
            font-weight: 500;
            color: #555;
        }
        .config-value {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Stress Test Report</h1>
            <div class="subtitle">Test ID: 1752697710 | Generated: 2025-07-16 20:28:30 UTC</div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card success">
                <div class="metric-value">0.0%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0ms</div>
                <div class="metric-label">Avg Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.10</div>
                <div class="metric-label">Requests/sec</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">6</div>
                <div class="metric-label">Total Requests</div>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">📊 Response Time Distribution</div>
            <canvas id="histogramChart" width="400" height="200"></canvas>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">0ms</div>
                <div class="metric-label">P50 (Median)</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">0ms</div>
                <div class="metric-label">P90</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">0ms</div>
                <div class="metric-label">P95</div>
            </div>
            <div class="metric-card danger">
                <div class="metric-value">0ms</div>
                <div class="metric-label">P99</div>
            </div>
        </div>

        <div class="config-section">
            <h3>⚙️ Test Configuration</h3>
            <div class="config-grid">
                <div class="config-item">
                    <span class="config-label">Target URL:</span>
                    <span class="config-value">https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Model:</span>
                    <span class="config-value">Qwen/Qwen2.5-VL-32B-Instruct</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Concurrency:</span>
                    <span class="config-value">3</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Total Requests:</span>
                    <span class="config-value">6</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Max Tokens:</span>
                    <span class="config-value">80</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Temperature:</span>
                    <span class="config-value">0.10</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Streaming:</span>
                    <span class="config-value">true</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Duration:</span>
                    <span class="config-value">60.01s</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Histogram Chart
        const ctx = document.getElementById('histogramChart').getContext('2d');
        const histogramChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ["0-1","1-2","2-3","3-4","4-5","5-6","6-7","7-8","8-9","9-10","10-11","11-12","12-13","13-14","14-15","15-16","16-17","17-18","18-19","19-20"],
                datasets: [{
                    label: 'Request Count',
                    data: [6,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Response Time Distribution (ms)'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Requests'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
