{"errors": {"error_counts": {}, "error_examples": []}, "latency_metrics": {"average_ms": 3222, "max_ms": 4376, "min_ms": 2456, "percentiles_ms": {"p50": 2836, "p90": 4376, "p95": 4376, "p99": 4376}}, "multimodal_metrics": {"average_image_processing_time_ms": 0.0, "average_video_processing_time_ms": 0.0, "image_processing_errors": 0, "images_processed": 0, "total_image_size_bytes": 0, "video_processing_errors": 0, "videos_processed": 0}, "summary": {"failed_requests": 0, "qps": 0.31014032079131476, "success_rate": 1.0, "successful_requests": 3, "test_duration_seconds": 9.673040875, "tokens_per_second": 29.670090689035778, "total_requests": 3}, "test_config": {"api_base": "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions", "concurrency": 1, "max_tokens": 100, "model": "Qwen/Qwen2.5-VL-32B-Instruct", "multimodal": false, "stream": true, "temperature": 0.10000000149011612, "total_requests": 3}, "test_id": "1752695476", "timestamp": "1752695476", "token_metrics": {"average_tokens_per_request": 95.66666666666667, "total_completion_tokens": 287, "total_prompt_tokens": 0, "total_tokens": 287}}